module.exports = {
  extends: [require.resolve('@galaxy/dhr-standard/dist/eslint.js')],
  rules: {
    'max-len': [
      'error',
      {
        code: 300,
        ignoreRegExpLiterals: true,
        ignoreUrls: true,
        ignoreTemplateLiterals: true,
      },
    ],
    // 暂时屏蔽
    // 'react/no-unused-class-component-methods': 0,
    // 'consistent-return': 0,
    // 'no-param-reassign': 0,
    'react/jsx-no-useless-fragment': 0,
    'no-param-reassign': 0,
    'max-lines-per-function': 0,
  },
}
