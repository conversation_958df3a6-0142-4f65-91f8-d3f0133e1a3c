{"name": "dhr-employee-relations-webapp", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"cz": "git-cz", "ready": "yarn", "lint": "eslint ./src --ext js,jsx,ts,tsx --cache", "lint-fix": "eslint --fix --ext .js,jsx,ts,tsx src/", "clean:lib": "rimraf ./lib", "clean:dist": "rimraf ./dist", "predll": "npm run clean:lib", "install:editor": "yalc add @amazebird/editor-dhr-material @amazebird/editor-draggable @amazebird/editor @amazebird/player @amazebird/register", "update:editor": "yalc update @amazebird/editor-dhr-material @amazebird/editor-draggable @amazebird/editor @amazebird/player @amazebird/register", "remove:editor": "yalc remove @amazebird/editor-dhr-material @amazebird/editor-draggable @amazebird/editor @amazebird/player @amazebird/register", "----- webpack开发模式 -----": "", "local": "cross-env NODE_ENV=local BUILD_ENV=development webpack-dev-server  --config ./build/dev.config.js", "dev": "cross-env NODE_ENV=dev BUILD_ENV=development webpack-dev-server --config ./build/dev.config.js", "test": "cross-env NODE_ENV=test BUILD_ENV=development webpack-dev-server   --config ./build/dev.config.js", "pre": "cross-env NODE_ENV=pre BUILD_ENV=development webpack-dev-server   --config ./build/dev.config.js", "prod": "cross-env NODE_ENV=prod BUILD_ENV=development webpack-dev-server  --config ./build/dev.config.js", "analize": "cross-env NODE_ENV=prod BUILD_ENV=production NODE_ANA=analyze webpack --progress --config ./build/microBuild.config.js", "----- vite开发模式 -----": "", "vite:local": "cross-env NODE_ENV=local BUILD_ENV=development vite  --config ./build/vite.config.js", "vite:dev": "cross-env NODE_ENV=test BUILD_ENV=development vite  --config ./build/vite.config.js", "vite:test": "cross-env NODE_ENV=test BUILD_ENV=development vite  --config ./build/vite.config.js", "----- 微前端本地开发模式 -----": "", "micro:dev": "cross-env NODE_ENV=dev  webpack-dev-server --config ./build/micro.config.js", "micro:test": "cross-env NODE_ENV=test  webpack-dev-server   --config ./build/micro.config.js", "micro:pre": "cross-env NODE_ENV=pre  webpack-dev-server   --config ./build/micro.config.js", "micro:prod": "cross-env NODE_ENV=prod  webpack-dev-server  --config ./build/micro.config.js", "----- 微前端+生成前端静态资源包 -----": "", "build:dev": "cross-env NODE_ENV=dev  webpack --progress --config ./build/microBuild.config.js", "build:test": "cross-env NODE_ENV=test  webpack --progress --config ./build/microBuild.config.js", "build:pre": "cross-env NODE_ENV=pre  webpack --progress --config ./build/microBuild.config.js", "build:prod": "cross-env NODE_ENV=prod  webpack --progress --config ./build/microBuild.config.js", "prepare": "husky install"}, "repository": {"type": "git"}, "author": "", "license": "ISC", "browserslist": [">0.2%", "not dead", "ie >= 9", "not op_mini all"], "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-import-replacement": "^0.1.1", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-proposal-decorators": "^7.16.4", "@babel/plugin-proposal-do-expressions": "^7.16.0", "@babel/plugin-proposal-export-default-from": "^7.16.0", "@babel/plugin-proposal-export-namespace-from": "^7.16.0", "@babel/plugin-proposal-function-bind": "^7.16.0", "@babel/plugin-proposal-function-sent": "^7.16.0", "@babel/plugin-proposal-json-strings": "^7.16.0", "@babel/plugin-proposal-logical-assignment-operators": "^7.16.0", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.0", "@babel/plugin-proposal-numeric-separator": "^7.16.0", "@babel/plugin-proposal-optional-chaining": "^7.16.0", "@babel/plugin-proposal-pipeline-operator": "^7.16.0", "@babel/plugin-proposal-throw-expressions": "^7.16.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-modules-commonjs": "^7.16.0", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@babel/preset-typescript": "^7.16.7", "@babel/runtime-corejs3": "^7.20.7", "@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^17.6.1", "@galaxy/commit-config": "^0.0.2", "@galaxy/dhr-standard": "0.0.6", "@originjs/vite-plugin-commonjs": "^1.0.2", "@pupu/apm-upload-sourcemap-plugin": "^2.0.1", "@types/clean-webpack-plugin": "^0.1.3", "@types/history": "^4.7.9", "@types/lodash-es": "4.17.6", "@types/react": "18.2.0", "@types/react-copy-to-clipboard": "^5.0.4", "@types/react-transition-group": "^4.4.4", "add-asset-html-webpack-plugin": "^3.2.0", "antd-dayjs-webpack-plugin": "^1.0.6", "awesome-typescript-loader": "^5.2.1", "babel-loader": "^8.2.3", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-import": "^1.13.3", "clean-webpack-plugin": "^4.0.0", "commitizen": "^4.3.0", "compression-webpack-plugin": "^9.0.1", "copy-webpack-plugin": "^10.0.0", "cross-env": "^7.0.3", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.0.2", "cz-conventional-changelog": "^3.3.0", "cz-git": "^1.6.1", "eslint": "^8.5.0", "file-loader": "^6.2.0", "file-system": "^2.2.2", "happypack": "^5.0.1", "html-webpack-deploy-plugin": "^3.0.0", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.0", "less": "^4.1.2", "less-loader": "^10.2.0", "lint-staged": "^13.2.1", "mini-css-extract-plugin": "^2.4.5", "mocker-api": "^2.9.4", "mockjs": "^1.1.0", "postcss": "^8.4.21", "postcss-loader": "^7.1.0", "postcss-preset-env": "^8.0.1", "pre-commit": "^1.2.2", "prettier": "^2.8.5", "resolve-url-loader": "^4.0.0", "rimraf": "^3.0.2", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "thread-loader": "^3.0.4", "typescript": "^4.5.2", "vite": "^2.7.2", "vite-plugin-env-compatible": "^1.1.1", "vite-plugin-html": "^2.1.2", "webpack": "5.65.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^4.10.0", "webpack-dev-server": "4.7.1", "webpack-merge": "^5.8.0"}, "dependencies": {"@amazebird/antd-business-field": "3.3.2", "@amazebird/antd-field": "3.1.1", "@amazebird/antd-schema-form": "3.1.1", "@amazebird/antd-schema-table": "3.1.1", "@amazebird/editor-component": "0.0.2", "@amazebird/schema-form": "^3.1.1", "@amazebird/table-business-field": "^3.1.1", "@amazebird/utils": "^3.3.2", "@ant-design/icons": "^4.7.0", "@ant-design/pro-form": "^1.52.13", "@ant-design/pro-layout": "^6.37.0", "@ant-design/pro-table": "2.76.3", "@galaxy/async-task-component": "0.1.4", "@galaxy/business-request": "^3.2.11", "@galaxy/component": "^0.2.0", "@galaxy/config-provider": "^0.1.2", "@galaxy/dhr-style": "0.2.4-alpha.3", "@galaxy/dict": "3.8.0", "@galaxy/org-selector": "4.4.0", "@galaxy/rbac": "^3.7.0", "@galaxy/rbac-component": "^3.7.0", "@galaxy/request": "^2.0.1", "@galaxy/tree-select": "0.0.14", "@galaxy/uc": "^3.3.22", "@galaxy/uc-component": "3.2.7-alpha.0", "@pupu/uc-sdk": "^0.20.0", "@galaxy/upload": "3.4.20-beta.0", "@galaxy/upload-component": "3.2.8", "@galaxy/user-selector": "4.6.0", "@galaxy/utils": "^1.1.0-alpha.1", "@pupu/apm-browser": "^1.20.1", "@pupu/brick-upload": "^2.6.0", "@types/dompurify": "^3.0.2", "@wangeditor/editor": "^5.1.23", "@pdf-lib/fontkit": "^1.1.1", "@wangeditor/editor-for-react": "^1.0.6", "ahooks": "^3.5.0", "antd": "4.24.13", "aws-sdk": "^2.1174.0", "axios": "^0.24.0", "babel-polyfill": "^6.26.0", "classnames": "^2.3.2", "cropperjs": "^1.5.13", "dayjs": "^1.11.7", "dompurify": "^3.0.4", "file-saver": "^2.0.5", "immer": "^10.0.2", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "mobx": "^6.3.8", "mobx-react": "^7.2.1", "moment": "^2.29.3", "normalize.css": "^8.0.1", "prop-types": "^15.7.2", "pdf-lib": "^1.17.1", "qrcode.react": "^3.1.0", "query-string": "^8.1.0", "react": "18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-cropper": "^2.3.3", "react-dom": "18.2.0", "react-query": "^3.39.3", "react-router-dom": "^6.4.3", "regenerator-runtime": "^0.13.11", "zustand": "^4.3.9"}, "engines": {"node": ">=20", "pnpm": "8.*.*"}, "config": {"commitizen": {"path": "./node_modules/cz-git", "czConfig": "./node_modules/@galaxy/dhr-standard/dist/cz.config.js"}}, "packageManager": "pnpm@8.15.9+sha512.499434c9d8fdd1a2794ebf4552b3b25c0a633abcee5bb15e7b5de90f32f47b513aca98cd5cfd001c31f0db454bc3804edccd578501e4ca293a6816166bbd9f81"}