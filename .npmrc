auto-install-peers=true
# 线上构建的时候拿不到最新的外网包，为了统一开发和线上构建，直接都用朴朴源
registry=http://mvn.pupuvip.com/repository/npm-group/
# registry=https://registry.npm.taobao.org
@amazebird:registry=http://mvn.pupuvip.com/repository/npm-group/
@galaxy:registry=http://mvn.pupuvip.com/repository/npm-group/
@pupu:registry=http://mvn.pupuvip.com/repository/npm-group/
sass_binary_site=https://npm.taobao.org/mirrors/node-sass/
phantomjs_cdnurl=https://npm.taobao.org/mirrors/phantomjs/
electron_mirror=https://npm.taobao.org/mirrors/electron/
