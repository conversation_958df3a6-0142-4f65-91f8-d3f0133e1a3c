@import './global-variable.less';
html,
body {
  font-weight: @bodyFontWeight;
}

ol,
ul {
  list-style: none;
}

[class^='homePage'] {
  width: 1200px;
  margin: 0 auto;
}

.@{ant-prefix}-design-pro.@{ant-prefix}-pro-basicLayout {
  .@{ant-prefix}-layout-sider {
    .@{ant-prefix}-layout-sider-children {
      // 顶部logo
      .@{ant-prefix}-pro-sider-logo {
        color: @sideLogoFontColor;
        .pupulogo {
          color: @logColor;
        }
      }

      // antd新版本使用margin-left做间距，但是旧版本使用icon做间距，导致主应用菜单间距变大
      .@{ant-prefix}-menu
        .@{ant-prefix}-menu-submenu
        .@{ant-prefix}-pro-menu-item
        .@{ant-prefix}-pro-menu-item-title {
        margin-left: 0;
      }
    }
  }
}

:global {
  .disabledClick {
    color: rgba(0, 0, 0, 0.25) !important;
    cursor: not-allowed !important;
  }
}
