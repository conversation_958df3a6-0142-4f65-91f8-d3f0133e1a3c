import React from 'react'
import { ContainerOutlined } from '@ant-design/icons'
import mainRoutes from './mainRoutes'
import type { Route, RouteInterface } from '../RouteFactory'

const baseRoutes: RouteInterface[] = [
  {
    name: '个人中心',
    icon: <ContainerOutlined rev={undefined} />,
    path: '/user',
    component: '/user/index.tsx',
    hideInMenu: true,
  },
  {
    name: '403',
    path: `/403`,
    icon: <ContainerOutlined rev={undefined} />,
    component: '/exception/403/index.tsx',
    hideInMenu: true,
  },
  {
    name: '404',
    path: `/404`,
    icon: <ContainerOutlined rev={undefined} />,
    component: '/exception/404/index.tsx',
    hideInMenu: true,
  },
  {
    name: '500',
    path: `/500`,
    icon: <ContainerOutlined rev={undefined} />,
    component: '/exception/500/index.tsx',
    hideInMenu: true,
  },
  // {
  //   name: 'Demo',
  //   path: `/demo`,
  //   icon: <ContainerOutlined rev={undefined} />,
  //   component: '/demo/index.tsx',
  //   hideInMenu: true,
  // },
]

const route: Route = {
  path: '/',
  routes: mainRoutes.concat(baseRoutes),
}

export default route
