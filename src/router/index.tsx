import React, { Component } from 'react'
import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom'
import AsyncComponent from '@/utils/AsyncComponent'
import config from '@/config'
import authWrapper from '@/mainApp/decorators/authWrapper'
import rbacWrapper from '@/mainApp/decorators/rbacWrapper'
import RouteFactory from '@/router/RouteFactory'
import route from '@/router/route'
import { isLocalNetwork } from '@/utils/utils'
import { EXTERNAL_USE_PAGE_ROUTE } from '@/constants'
import DHRContainer from '@/components/DHRContainer'

const { loginUrl, baseRoute } = config

// 在本地的时候默认包上默认权限用于单独开发，但是在线上独立访问时不需要实体权限
const Layout = isLocalNetwork()
  ? rbacWrapper(AsyncComponent(() => import('@/layouts/BaseLayout')))
  : AsyncComponent(() => import('@/layouts/BaseLayout'))

const BaseLayout = authWrapper(Layout)
const MicroLayout = authWrapper(AsyncComponent(() => import('@/layouts/MicroLayout')))
const Login = AsyncComponent(() => import('@/mainApp/login-manage/login'))

const routeFactory = new RouteFactory(route)
const routes = routeFactory.transformList()
class Router extends Component {
  state = {}

  /* eslint-disable class-methods-use-this */
  renderElement = (item) => {
    const GlobalCompoent = item.component

    const PageElement = (
      <DHRContainer defaultTitle={item.name}>
        <GlobalCompoent />
      </DHRContainer>
    )

    return PageElement
  }

  render() {
    // const LayoutCom = window.__POWERED_BY_QIANKUN__ ? MicroLayout : BaseLayout
    const LayoutCom =
      window.__POWERED_BY_QIANKUN__ ||
      EXTERNAL_USE_PAGE_ROUTE.find((item) => window.location.pathname.includes(item))
        ? MicroLayout
        : BaseLayout
    return (
      <BrowserRouter basename={baseRoute}>
        <Routes>
          {/* 从 / 重定向到 baseRoute, 防止无限循环渲染，需添加相关判断 */}
          {/* {baseRoute && baseRoute !== '/' ? (
            <Route element={<Navigate to={baseRoute} />} path="/" />
          ) : null} */}
          <Route path="/login" element={<Login />} />
          <Route path="/" element={<LayoutCom />}>
            {routes.map((item) => {
              const hasToken = true
              const routeElement = (
                <Route
                  key={`${item.path}${item.name}`}
                  path={item.path}
                  element={this.renderElement(item)}
                />
              )
              if (hasToken) {
                return routeElement
              }
              return (
                <Route key="login" path="*" element={<Navigate to={{ pathname: loginUrl }} />} />
              )
            })}
          </Route>
        </Routes>
      </BrowserRouter>
    )
  }
}

export default Router
