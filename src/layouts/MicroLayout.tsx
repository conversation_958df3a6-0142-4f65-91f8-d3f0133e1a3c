import React from 'react'
import { isMobile } from '@galaxy/utils'
import { Outlet } from 'react-router-dom'
import cls from 'classnames'
import { EXTERNAL_USE_PAGE_ROUTE } from '@/constants'
import styles from './BaseLayout.less'

const MicroLayout = () => {
  const isH5 = isMobile()
  const isExternalPage = EXTERNAL_USE_PAGE_ROUTE?.find((el) =>
    window.location.pathname.includes(el),
  )

  return (
    <div className={cls(!isH5 && styles.micro_layout_box, isExternalPage && styles.employ_oa_page)}>
      <Outlet />
    </div>
  )
}
export default MicroLayout
