import React, { useState } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { MenuFoldOutlined, MenuUnfoldOutlined } from '@ant-design/icons'
import ProLayout, { PageContainer } from '@ant-design/pro-layout'
import queryString from 'query-string'
import config from '@/config'
import WaterMark from '@/components/base/WaterMark'
import User from '@/components/base/User'
import RouteFactory from '@/router/RouteFactory'
import route from '@/router/route'
import './BaseLayout.less'

interface StateProps {
  isHideNav: string
  isHideMenu: string
}

const routeFactory = new RouteFactory(route)

const { projectName, baseRoute } = config

const BaseLayout: React.FC = function () {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { isHideNav = false, isHideMenu = false } = queryString.parse(
    location.search,
  ) as unknown as StateProps

  return (
    <div style={{ minWidth: 1200, overflowX: 'auto', height: '100%' }}>
      <ProLayout
        menuRender={isHideMenu ? false : undefined}
        breadcrumbRender={isHideNav ? false : undefined}
        headerRender={isHideNav ? false : undefined}
        route={routeFactory.basenameRoute}
        fixedHeader={false}
        fixSiderbar
        // title={projectName}
        id="test-pro-layout"
        collapsed={collapsed}
        collapsedButtonRender={false}
        onCollapse={setCollapsed}
        menuHeaderRender={() => (
          <div id="customize_menu_header" onClick={() => navigate('/')}>
            {collapsed ? (
              <span className="pupulogo" style={{ fontSize: '14px', marginRight: '5px' }}>
                pupu
              </span>
            ) : (
              <span className="pupulogo" style={{ fontSize: '20px', marginRight: '5px' }}>
                pupu
              </span>
            )}
            {collapsed ? '' : projectName}
          </div>
        )}
        menuItemRender={(item, dom) => (
          <a
            onClick={() => {
              if (item.path) {
                const reg = new RegExp(baseRoute)
                // ProLayout 匹配面包屑需要带有basename,导致点击菜单跳转时会带有双重basename
                navigate(item.path.replace(reg, ''))
              }
            }}
          >
            {dom}
          </a>
        )}
        headerContentRender={() => (
          <div
            onClick={() => setCollapsed(!collapsed)}
            style={{
              display: 'inline-block',
              padding: '0 10px',
              cursor: 'pointer',
              fontSize: '16px',
            }}
          >
            {collapsed ? (
              <MenuUnfoldOutlined rev={undefined} />
            ) : (
              <MenuFoldOutlined rev={undefined} />
            )}
          </div>
        )}
        rightContentRender={() => (
          <div style={{ marginRight: '10px' }}>
            {/* <Theme /> */}
            <User />
          </div>
        )}
      >
        <PageContainer
          /**
           * head 通过dhr-style 代理设置
           * 此处如果不使用pageContainer的header，可以这样设置
           * 如果不设置header={{ title: null }} ,proLayout会读取route中的name设置
           */
          header={{ title: null }}
          style={{
            minWidth: '1150px',
          }}
        >
          <div style={{ minHeight: 'calc(100vh - 200px)' }}>
            <WaterMark>
              <Outlet />
            </WaterMark>
          </div>
        </PageContainer>
      </ProLayout>
    </div>
  )
}

export default BaseLayout
