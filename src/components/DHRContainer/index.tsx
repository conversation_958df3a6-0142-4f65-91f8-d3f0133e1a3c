import React, { useEffect } from 'react'
import { <PERSON>hr<PERSON><PERSON><PERSON> as Container } from '@galaxy/dhr-style'
import { useStore } from '@/stores'
// import ExtraWrap from '@/components/baseContainer/extraWrap'

interface IProps {
  /**
   * 路由组件
   */
  children: any
  /**
   * 返回按钮
   */
  needBack?: any
  /**
   * 通过路由名称传入的默认 title
   */
  defaultTitle?: React.ReactNode
}

const DHRContainer: React.FC<IProps> = function ({ children, defaultTitle, needBack }: IProps) {
  const store = useStore((state) => state)
  const extraElement = useStore((state) => state.extra)
  const { setTitle, setNeedBack, title } = store

  useEffect(() => {
    setNeedBack(needBack)
    setTitle(React.isValidElement(defaultTitle) ? defaultTitle : <span>{defaultTitle}</span>)
  }, [defaultTitle])

  return (
    <Container
      // todo
      title={title}
      needBack={needBack}
      extra={extraElement}
      unableToSticky
      noStyle
    >
      {children}
    </Container>
  )
}

export default DHRContainer
