import React, { CSSProperties, useEffect, useImperative<PERSON>andle, useMemo, useState } from 'react'
import { Spin, SpinProps } from 'antd'
import styles from './index.less'

export type ILoaderProps = {
  position?: 'absolute' | 'fixed'
  lockScroll?: boolean
} & SpinProps

export type ILoaderHandler = {
  loading?: (status?: boolean, tips?: string) => void
}

const Loader = (props: ILoaderProps, ref: React.Ref<ILoaderHandler>) => {
  const { position, lockScroll = false, ...rest } = props
  const [loading, setLoading] = useState(false)
  const [tip, setTip] = useState(props.tip || '')

  useEffect(() => {
    if (!loading) {
      document.body.classList.remove('overflow-hidden')
    }
  }, [loading])

  useEffect(() => {
    if ((props.spinning || loading) && lockScroll) {
      document.body.classList.add('overflow-hidden')
    }
    return () => {
      if (lockScroll) {
        document.body.classList.remove('overflow-hidden')
      }
    }
  }, [loading, lockScroll, props.spinning])

  useImperativeHandle(ref, () => ({
    loading: (status = true, loadingTip = '') => {
      setLoading(status)
      setTip(loadingTip)
    },
  }))

  const style = useMemo(() => {
    const styleObj: CSSProperties = {}
    if (position) {
      styleObj.position = position
    }
    return styleObj
  }, [position])
  if (!loading) {
    return null
  }
  return (
    <div className={styles.globalLoader} style={style}>
      <Spin {...rest} spinning={loading} tip={tip}>
        <div className={styles.globalLoaderMask} />
      </Spin>
    </div>
  )
}

export default React.forwardRef(Loader)
