/* eslint-disable react/jsx-no-constructed-context-values */
import React, { useContext, useEffect, useMemo, useRef } from 'react'
import { ConfigProvider as AndConfigProvider } from 'antd'
import zhCN from 'antd/lib/locale/zh_CN'
import { CLIENT_CODES } from '@/constants'
import { createRoot } from 'react-dom/client'
import Loader, { ILoaderProps } from '.'

export type ILoadOperation = {
  show: (tip?: string | React.ReactNode) => void
  hide: (delay?: number, cb?: VoidFunction) => void
}

type CallbackOptions = {
  loading: (status?: boolean, tips?: string | React.ReactNode) => void
  destroy: VoidFunction
}

type InstanceCallback = (opts: CallbackOptions) => void

const createInstance = (props: ILoaderProps, callback: InstanceCallback) => {
  const div = document.createElement('div')
  document.body.appendChild(div)
  const root = createRoot(div)
  root.render(
    <AndConfigProvider locale={zhCN} prefixCls={CLIENT_CODES.antdPrefixCls}>
      <Loader
        ref={(ref: any) => {
          callback({
            loading: ref?.loading,
            destroy: () => {
              root.unmount()
              div.remove()
            },
          })
        }}
        {...props}
      />
    </AndConfigProvider>,
  )
}

export function useLoading(props?: ILoaderProps): ILoadOperation {
  const instance = useRef<CallbackOptions>()

  const operation = useMemo(
    () => ({
      show: (tip?: string | React.ReactNode) => instance.current?.loading?.(true, tip),
      hide: (delay?: number, cb?: VoidFunction) => {
        const targetDelay = delay || 0
        if (targetDelay <= 0) {
          instance.current?.loading?.(false)
          cb?.()
        } else {
          const timer = setTimeout(() => {
            instance.current?.loading?.(false)
            cb?.()
            clearTimeout(timer)
          }, targetDelay)
        }
      },
    }),
    [instance.current],
  )

  useEffect(() => {
    const loadingProps = { position: 'fixed', lockScroll: true, ...props } as ILoaderProps
    createInstance(loadingProps, (opts: CallbackOptions) => {
      instance.current = opts
    })
    return () => {
      instance.current?.destroy()
      instance.current = undefined
    }
  }, [])

  return operation
}

export const LoaderContext = React.createContext<{
  loader: ILoadOperation | undefined
}>({
  loader: undefined,
})

export const useLoaderContext = () => useContext(LoaderContext)

export function LoaderContextDecorator(Component) {
  return (props) => {
    const loader = useLoading()
    return (
      <LoaderContext.Provider value={{ loader }}>
        <Component {...props} />
      </LoaderContext.Provider>
    )
  }
}
