import { Modal, ModalFuncProps } from 'antd'

export const confirmDialog = (props: ModalFuncProps) => {
  Modal.confirm({
    okText: '确定',
    cancelText: '取消',
    ...props,
  })
}

export const infoDialog = (props: ModalFuncProps) => {
  Modal.info({
    okText: '知道了',
    ...props,
  })
}

export const successDialog = (props: ModalFuncProps) => {
  Modal.success({
    okText: '知道了',
    ...props,
  })
}

export const errorDialog = (props: ModalFuncProps) => {
  Modal.error({
    okText: '知道了',
    ...props,
  })
}

export const warningDialog = (props: ModalFuncProps) => {
  Modal.warning({
    okText: '知道了',
    ...props,
  })
}
