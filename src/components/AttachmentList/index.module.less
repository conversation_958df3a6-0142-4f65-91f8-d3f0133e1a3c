.labelCol {
  display: flex;
  justify-content: flex-end;
  padding-right: 8px;
}

.wrapperCol {
  .attachmentContainer {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
  }

  .fileList {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.firstFileItem {
  display: flex;
  align-items: center;

  &:hover {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 4px 8px;
    margin: -4px -8px;
  }
}

.fileItem {
  display: flex;
  align-items: center;
  padding: 4px 0;

  &:hover {
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 4px 8px;
    margin: 0 -8px;
  }
}
