import React, { useEffect, useState } from 'react'
import { Button, Col, Row } from 'antd'
import { FileOutlined, LoadingOutlined } from '@ant-design/icons'
import { uploadSdk } from '@/services/upload'
import { map } from 'lodash-es'
import { isMobile } from '@galaxy/utils'
import { processFileWatermark, cleanupBlobUrl } from '@/utils/waterMark/WaterMarkFile'
import styles from './index.module.less'

type FileItem = {
  name: string
  uuid: string
}

interface IProps {
  fileList: FileItem[]
}

const basicProps = {
  labelCol: { flex: '135px' },
  ...(isMobile() ? { wrapperCol: { flex: '1' } } : { wrapperCol: { span: 17 } }),
}

const AttachmentList: React.FC<IProps> = (props) => {
  const { fileList } = props
  const [urlList, setUrlList] = useState<Record<string, string>>({})
  const [processingFiles, setProcessingFiles] = useState<Set<string>>(new Set())

  useEffect(() => {
    if (fileList && fileList.length > 0) {
      // 设置所有文件为处理中状态
      const fileUuids = fileList.map((file) => file.uuid)
      setProcessingFiles(new Set(fileUuids))

      Promise.all(
        map(fileList, async (item) => {
          try {
            // 下载原始文件
            const result = await uploadSdk.download(item.uuid as string)
            if (!result?.url) {
              return { uuid: item.uuid, url: null }
            }

            // 处理水印
            const watermarkResult = await processFileWatermark({
              fileUrl: result.url,
              uuid: item.uuid,
              fileName: item.name,
            })

            if (watermarkResult.error) {
              console.warn(`文件水印处理失败: ${item.name}`, watermarkResult.error)
              // 水印处理失败时使用原始文件
              return { uuid: item.uuid, url: result.url }
            }

            return { uuid: item.uuid, url: watermarkResult.processedUrl }
          } catch (error) {
            console.error('处理文件失败:', error)
            return { uuid: item.uuid, url: null }
          }
        }),
      ).then((results) => {
        const urlMap = {}
        results.forEach((result) => {
          if (result.url) {
            urlMap[result.uuid] = result.url
          }
        })
        setUrlList(urlMap)
        // 清除所有处理中状态
        setProcessingFiles(new Set())
      })
    }
  }, [fileList])

  // 清理水印URL，释放内存
  useEffect(() => {
    return () => {
      Object.values(urlList).forEach((url) => {
        if (url && url.startsWith('blob:')) {
          cleanupBlobUrl(url)
        }
      })
    }
  }, [urlList])

  const FileItem = (file: FileItem, index: number) => {
    const fileUrl = urlList[file.uuid]
    const isProcessing = processingFiles.has(file.uuid)

    const handleClick = () => {
      if (fileUrl && !isProcessing) {
        window.open(fileUrl, '_blank')
      }
    }

    return (
      <div key={file.uuid} className={index === 0 ? styles.firstFileItem : styles.fileItem}>
        {isProcessing ? (
          <LoadingOutlined style={{ marginRight: 8, color: '#1890ff' }} />
        ) : (
          <FileOutlined style={{ marginRight: 8 }} />
        )}
        <Button
          type="link"
          style={{
            padding: 0,
            height: 'auto',
            textAlign: 'start',
          }}
          onClick={handleClick}
          size="small"
          disabled={isProcessing}
        >
          {isProcessing ? '正在加载中...' : file.name}
        </Button>
      </div>
    )
  }

  if (!fileList || fileList.length === 0) {
    return null
  }

  return (
    <Row gutter={0} align="top">
      <Col {...basicProps.labelCol} className={styles.labelCol}>
        <span className={styles.key}>附件：</span>
      </Col>
      <Col {...basicProps.wrapperCol} className={styles.wrapperCol}>
        <div className={styles.attachmentContainer}>{FileItem(fileList[0], 0)}</div>
        {fileList.length > 1 && (
          <div className={styles.fileList}>
            {fileList.slice(1).map((item, index) => FileItem(item, index + 1))}
          </div>
        )}
      </Col>
    </Row>
  )
}

export default AttachmentList
