import React from 'react'
import { <PERSON><PERSON>, <PERSON>, message } from 'antd'
import { SchemaTable } from '@amazebird/antd-schema-table'
import {
  APPROVAL_STATUS_OPTIONS,
  APPROVAL_STATUS,
  INVOICE_TYPE_NUMBER_MAP,
  PAGE_MODE,
} from '@/constants'
import { deleteCompensationById } from '@/api/compensation'
import { deleteNoRecoveryById } from '@/api/security-no-recovery'
import { deleteSecurityReimbursementById } from '@/api/security-reimbursement'
import { useNavigate } from 'react-router-dom'
import { skipOa } from '@/utils/oa'
import styles from './index.module.less'
import { confirmDialog } from '../Dialog'

interface DocumentDTO {
  applyDate: number
  id: number
  name: string
  oaId: string
  oaNo: string
  signed: number
  type: number
  typeName: string
  userIdCreate: string
  userNameCreate: string
}

interface IProps {
  data: DocumentDTO[]
  userName: string
  userNum: string
  pageName: 'security' | 'employment-dispute'
}

const InvoiceTable: React.FC<IProps> = ({ data, pageName, userName, userNum }) => {
  const navigate = useNavigate()

  const handleJump = (record: DocumentDTO, mode) => {
    navigate(`/${pageName}/${INVOICE_TYPE_NUMBER_MAP[record.type]}?id=${record?.id}&mode=${mode}`)
  }

  const handleDelete = (record: DocumentDTO) => {
    confirmDialog({
      title: '删除',
      content: (
        <span>
          确定要删除事件员工
          <strong>
            {userName}（{userNum}）
          </strong>
          的{record.typeName}？
        </span>
      ),
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          let res
          switch (record.type) {
            // 赔偿额度申请
            case 3:
              res = await deleteCompensationById(record.id)
              break
            case 4:
              res = await deleteSecurityReimbursementById(record.id)
              break
            case 5:
              res = await deleteNoRecoveryById(record.id)
              break
            default:
              return
          }
          if (res?.data) {
            message.success('删除成功')
            window.location.reload()
          }
        } catch (error) {
          message.error('删除失败，请稍后重试')
        }
      },
    })
  }

  const renderOperator = (_, record: DocumentDTO) => {
    switch (record.signed) {
      case APPROVAL_STATUS.DRAFT:
      case APPROVAL_STATUS.REJECTED:
        return (
          <div className={styles.operator}>
            <Button type="link" onClick={() => handleJump(record, PAGE_MODE.detail)}>
              查看
            </Button>
            <Button type="link" onClick={() => handleJump(record, PAGE_MODE.edit)}>
              编辑
            </Button>
            <Button type="link" onClick={() => handleDelete(record)}>
              删除
            </Button>
          </div>
        )
      case APPROVAL_STATUS.PENDING:
      case APPROVAL_STATUS.APPROVED:
        return (
          <div className={styles.operator}>
            <Button type="link" onClick={() => handleJump(record, PAGE_MODE.detail)}>
              查看
            </Button>
          </div>
        )
      default:
        return null
    }
  }

  const columns = [
    {
      title: '流程名称',
      dataIndex: 'name',
      cell: 'Text',
    },
    {
      title: 'OA流程编号',
      dataIndex: 'oaId',
      width: 150,
      render: (value, record) => {
        return value ? (
          <a target="_blank" rel="noreferrer" onClick={() => skipOa(record?.oaNo)}>
            {value}
          </a>
        ) : (
          '--'
        )
      },
    },
    {
      title: '审批状态',
      dataIndex: 'signed',
      render: (value: string) => {
        const options = APPROVAL_STATUS_OPTIONS.find((item) => item.value === value)
        return options?.label || '--'
      },
    },
    {
      title: '申请人',
      dataIndex: 'userIdCreate',
      render: (_, record: DocumentDTO) => {
        return <div>{`${record.userNameCreate}（${record.userIdCreate}）`}</div>
      },
    },
    {
      title: '申请时间',
      dataIndex: 'applyDate',
      cell: 'Date',
    },
    {
      key: '_operator',
      title: '操作',
      width: 250,
      render: renderOperator,
    },
  ]
  return (
    <Card bodyStyle={{ padding: 0 }}>
      <SchemaTable
        dataSource={data}
        columns={columns}
        rowKey="id"
        pagination={false}
        size="small"
        showHeader
      />
    </Card>
  )
}

export default InvoiceTable
