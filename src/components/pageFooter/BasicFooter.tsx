import React, { HTMLProps, SyntheticEvent } from 'react'
import { Button } from 'antd'
import { Submit } from '@amazebird/antd-schema-form'

import styles from './style.module.less'
import { confirmDialog } from '../Dialog'

interface IProps {
  showCancel?: boolean
  showConfirm?: boolean
  confirmText?: string
  cancelText?: string
  loading?: boolean
  // 是否使用 schema 的 Submit 组件进行提交
  useSchemaSubmit?: boolean
  // 是否展现modal提示，可以是一个判断函数
  showModal?: boolean | (() => boolean)
  handleConfirm?: (e?: SyntheticEvent | any) => void
  handleCancel?: (e?: SyntheticEvent) => void
}

const BasicFooter: React.FC<IProps & HTMLProps<HTMLDivElement>> = ({
  showCancel = true,
  showConfirm = true,
  loading = false,
  cancelText,
  confirmText,
  useSchemaSubmit = true,
  showModal = false,
  handleCancel,
  handleConfirm,
  ...props
}) => {
  const SubmitButton = () =>
    useSchemaSubmit ? (
      <Submit loading={loading} onFinish={handleConfirm} className={styles.confirm} type="primary">
        {confirmText || '保存'}
      </Submit>
    ) : (
      <Button loading={loading} onClick={handleConfirm} className={styles.confirm} type="primary">
        {confirmText || '保存'}
      </Button>
    )

  return (
    <div className={styles.footer} {...props}>
      {showCancel ? (
        <Button
          className={styles.cancel}
          onClick={() => {
            const visible = typeof showModal === 'function' ? showModal() : showModal
            if (visible) {
              confirmDialog({
                title: '项目内容未提交，确认要取消吗？',
                okText: '确认',
                cancelText: '取消',
                onOk: handleCancel,
              })
            } else {
              handleCancel?.()
            }
          }}
        >
          {cancelText || '取消'}
        </Button>
      ) : null}

      {showConfirm ? SubmitButton() : null}
    </div>
  )
}

export default BasicFooter
