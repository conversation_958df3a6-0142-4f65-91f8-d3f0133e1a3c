import React, { useEffect, useMemo } from 'react'
import { Item, SchemaForm } from '@amazebird/antd-schema-form'
import { Button, Descriptions } from 'antd'
import { filter, isArray, isObject, map } from 'lodash-es'
import moment from 'moment'
import CustomModal from '../CustomModal'
import styles from './index.less'

type IExportModalProps = {
  title?: string | React.ReactNode
  visible: boolean
  searchColumns: any[]
  searchValues: Record<string, any>
  excludeField?: string[]
  onExport: (params: Record<string, any>) => Promise<void>
  close: () => void
}

const ExportModal = (props: IExportModalProps) => {
  const { title, visible, searchColumns, searchValues, excludeField = [], onExport, close } = props
  const form = SchemaForm.createForm()

  const conditions = useMemo(() => {
    const fields = filter(searchColumns, (item) => !excludeField.includes(item?.key))
    const exportConditions: Record<string, any> = []
    map(fields, (field: any) => {
      const v: any = searchValues[field.key] || ''
      let value: any = v
      if (isArray(v)) {
        value = map(v, (item) => {
          if (isObject(item) && 'label' in item) {
            return item?.label || ''
          }
          return item
        }).join(',')
      }
      if (isArray(v) && field.component === 'RangePicker') {
        value = `${moment(v[0]).format('YYYY-MM')} ~ ${moment(v[1]).format('YYYY-MM')}`
      }
      if (isObject(v) && 'label' in v) {
        value = v?.label || ''
      }
      exportConditions.push({
        label: field?.title || field?.label || '',
        value: value || '全部',
      })
    })
    return exportConditions
  }, [searchColumns, searchValues])

  useEffect(() => {
    if (searchValues?.timeAccident) {
      form.setFieldsValue({ timeAccident: searchValues.timeAccident })
    } else {
      form.setFieldsValue({ timeAccident: undefined })
    }
  }, [searchValues])

  const schema = useMemo(() => {
    return {
      exportConditions: {
        label: '导出条件',
        component: () => (
          <Descriptions
            layout="horizontal"
            column={1}
            labelStyle={{ width: 110, justifyContent: 'flex-end' }}
          >
            {map(conditions, (item) => (
              <Descriptions.Item label={item.label}>{item.value}</Descriptions.Item>
            ))}
          </Descriptions>
        ),
      },
      timeAccident: {
        label: '案发时间',
        component: 'RangePicker',
        required: true,
        props: {
          style: { width: '100%' },
          picker: 'month',
          disabledDate: (current) => {
            const currentYearMonth = moment().endOf('year').format('YYYY-MM')
            const yearMonth = current.format('YYYY-MM')
            return yearMonth > currentYearMonth
          },
        },
      },
    }
  }, [conditions])

  const onOk = async () => {
    try {
      const values = await form.validateFields()
      onExport?.({ ...searchValues, ...values })
      // close()
    } catch (err) {
      console.log(err)
    }
  }
  const onCancel = () => {
    close()
  }

  return (
    <CustomModal
      title={title || '新建导出任务'}
      open={visible}
      width={600}
      onCancel={onCancel}
      destroyOnClose
      className={styles.exportModal}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="confirm" type="primary" onClick={onOk}>
          确定
        </Button>,
      ]}
    >
      <SchemaForm form={form} schema={schema} labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
        <Item field="exportConditions" />
        <Item field="timeAccident" />
      </SchemaForm>
    </CustomModal>
  )
}

export default ExportModal
