import React from 'react'
import { useStore } from '@/stores'
import styles from './style.less'

const Header: React.FC = function () {
  const extraElement = useStore((state) => state.extra)
  const titleElement = useStore((state) => state.title)
  return (
    <div className={styles.header}>
      <div className={styles.title}>{titleElement}</div>
      <div className={styles.extra}>{extraElement}</div>
    </div>
  )
}

export default Header
