import React, { useEffect } from 'react'
import { useStore } from '@/stores'
import Header from './Header'
import Content from './Content'
import Footer from './Footer'

interface IProps {
  children: any
  title?: string
  needBack?: boolean
}

const BaseContainer: React.FC<IProps> = function ({ children, title, needBack }: IProps) {
  const setTitle = useStore((state) => state.setTitle)
  const setNeedBack = useStore((state) => state.setNeedBack)

  useEffect(() => {
    setTitle(title)
    setNeedBack(needBack)
  }, [title, needBack])

  return (
    <>
      <Header />
      <Content>{children}</Content>
      <Footer />
    </>
  )
}
export default BaseContainer
