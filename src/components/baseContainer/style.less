.header {
  width: 100%;
  background-color: #fff;
  display: flex;
  padding: 8px 24px 16px 24px;
  justify-content: space-between;
  .title {
    color: rgba(0, 0, 0, 0.85);
    font-weight: normal;
    font-size: 20px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .extra {
    & > * {
      margin-left: 12px;
    }
  }
}

.content {
  [container-type="tabContainer"] {
    [class="formation-tabs-nav"] {
      padding: 0 24px;
      background-color: #fff;
      margin: 0;
    }
    [class="formation-tabs-content-holder"] {
      padding: 24px;
    }
  }
}
.closely {
  margin: 0;
}

.footer {
  height: 50px;
  padding: 0 10px;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: white;
  z-index: 99;
}
