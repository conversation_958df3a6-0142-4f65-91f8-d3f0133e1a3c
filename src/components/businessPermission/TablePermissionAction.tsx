import React, { useMemo } from 'react'
import { Dropdown, Menu, Button, Space } from 'antd'
import { DownOutlined } from '@ant-design/icons'
import { map, slice } from 'lodash-es'
import { hasPermission } from './utils'
import styles from './index.less'

export type PermissionActionItem = {
  key: string
  label: string | React.ReactNode
  order: number
  style?: any
  disabled?: boolean
  render?: () => JSX.Element
  permissionCode?: string
  customPermissionCheck?: () => boolean
}

type IProps = {
  actions: PermissionActionItem[]
  expandNumber?: number
  menuClick?: (key: string) => void
}

const getMenusButtons = (list: PermissionActionItem[], operatorClick?: (key: string) => void) =>
  map(list, (item) => {
    if (item.render) {
      return item.render()
    }
    return (
      <Button
        key={item.key}
        onClick={() => !item.disabled && operatorClick?.(item.key)}
        type="link"
        size="small"
        disabled={item.disabled}
        style={item.style}
      >
        {item.label}
      </Button>
    )
  })

const TablePermissionAction = (props: IProps) => {
  const { actions, expandNumber = 3, menuClick } = props

  // 权限过滤
  const { memoButtons, memoMenus } = useMemo(() => {
    const newActions: PermissionActionItem[] = map(actions, (item) => {
      if (hasPermission({ code: item?.permissionCode, customFunc: item?.customPermissionCheck })) {
        return item
      }
      return undefined
    }).filter(Boolean) as PermissionActionItem[]
    newActions.sort((a, b) => a.order - b.order)
    let buttons: React.ReactNode[] = []
    let menus: PermissionActionItem[] = []
    if (actions.length > expandNumber - 1) {
      buttons = getMenusButtons(slice(newActions, 0, expandNumber - 1), menuClick)
      menus = slice(newActions, expandNumber - 1, newActions.length)
    } else {
      buttons = getMenusButtons(newActions, menuClick)
    }
    return { memoButtons: buttons, memoMenus: menus }
  }, [actions])

  // 是否所有按钮禁止点击
  const menuAllDisabled = useMemo(() => {
    return memoMenus.every((menu) => menu?.disabled === true)
  }, [memoMenus])

  const renderFoldActions = (renderActions: PermissionActionItem[]) => {
    const items = map(renderActions, (action: PermissionActionItem) => ({
      label: action.render ? (
        action.render()
      ) : (
        <Button
          key={action.key}
          type="link"
          size="small"
          disabled={action.disabled}
          style={{
            fontSize: 12,
            ...action.style,
          }}
        >
          {action.label}
        </Button>
      ),
      key: action.key,
    }))
    return <Menu items={items} onClick={({ key }) => menuClick?.(key)} />
  }

  return (
    <Space className={styles.menus}>
      {memoButtons}
      {memoMenus.length > 0 && (
        <Dropdown
          key="more"
          trigger={['click']}
          disabled={menuAllDisabled}
          overlay={renderFoldActions(memoMenus)}
        >
          <a onClick={(e) => e.preventDefault()}>
            更多 <DownOutlined />
          </a>
        </Dropdown>
      )}
    </Space>
  )
}

export default TablePermissionAction
