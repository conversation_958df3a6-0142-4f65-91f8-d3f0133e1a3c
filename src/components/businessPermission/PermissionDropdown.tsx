import React, { useMemo } from 'react'
import { Dropdown, Menu, Button } from 'antd'
import { hasPermission } from './utils'
import style from './index.less'

export type PermissionActionItem = {
  key: string | number
  label: string | React.ReactNode
  style?: any
  disabled?: boolean
  render?: () => JSX.Element
  permissionCode?: string
  customPermissionCheck?: () => boolean
  children?: React.ReactNode
}

interface Iprops {
  actions: PermissionActionItem[]
  menuClick?: (key: any) => void
}

const PermissionDropdown: React.FC<Iprops> = (props) => {
  const { actions, menuClick, children } = props

  const permissionActions: PermissionActionItem[] = useMemo(() => {
    return actions
      .map((item) => {
        if (
          hasPermission({ code: item?.permissionCode, customFunc: item?.customPermissionCheck })
        ) {
          return item
        }
        return undefined
      })
      .filter(Boolean) as PermissionActionItem[]
  }, [actions])

  const hideDropDown = useMemo(() => {
    // 使用 every 方法来确定所有菜单项是否被禁用
    const isShow = !permissionActions.every((menu) => menu?.disabled)
    return isShow
  }, [permissionActions])

  const renderMenuItem = (permissionMenus: PermissionActionItem[]) => {
    const items = permissionMenus.map((action: PermissionActionItem) => {
      return {
        label: action.render ? (
          action.render()
        ) : (
          <Button key={action.key} disabled={action.disabled} type="text">
            {action.label}
          </Button>
        ),
        key: action.key,
      }
    })

    return <Menu items={items} onClick={({ key }) => menuClick?.(key)} className={style.menu} />
  }

  return hideDropDown ? (
    <Dropdown overlay={renderMenuItem(permissionActions)}>{children}</Dropdown>
  ) : null
}

export default PermissionDropdown
