import { registerComponent } from '@amazebird/antd-schema-form'
import { isNil } from 'lodash-es'
import { DictSelect } from './DictSelect'

registerComponent('DictSelect', DictSelect, DictSelect.Text, (field) => {
  const { placeholder, label, name, required } = field
  const FieldName = name || label
  if (FieldName) {
    if (isNil(placeholder)) {
      field.placeholder = `请选择${FieldName}`
    }
    if (required === true) {
      field.required = [true, `请选择${FieldName}`]
    }
  }
  if (isNil(field?.props?.allowClear)) {
    field.allowClear = true
  }
})
