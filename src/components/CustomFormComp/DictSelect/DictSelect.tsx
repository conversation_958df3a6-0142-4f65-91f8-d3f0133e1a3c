import React, { useMemo } from 'react'
import { Select, SelectProps } from 'antd'
import { defaultTo, isArray, isNil, map } from 'lodash-es'
import styles from './index.less'

type IProps = {
  hideDisabledOption?: boolean
  canSelectDisabledOption?: boolean
} & SelectProps

const NULL_TEXT = '--'
const DictSelect = (props: IProps) => {
  const {
    hideDisabledOption = true,
    canSelectDisabledOption = false,
    optionFilterProp = 'label',
    options: originalOptions,
    ...rest
  } = props
  const options = useMemo(() => {
    return map(originalOptions, (item) => {
      if (!canSelectDisabledOption) {
        item.disabled = !item.isEnabled
      }
      return item
    })
  }, [originalOptions, canSelectDisabledOption])

  return (
    <Select
      {...rest}
      optionFilterProp={optionFilterProp}
      popupClassName={hideDisabledOption ? styles.dictPopupClassName : undefined}
      options={options}
    />
  )
}

export const OptionText = ({ options, value, labelInValue }) => {
  let text = NULL_TEXT
  if (isNil(value)) {
    return text
  }
  if (labelInValue) {
    text = isArray(value) ? value.map((obj) => obj.label).join(',') : value.label
  } else {
    const optionsMap = new Map()
    // eslint-disable-next-line @typescript-eslint/no-shadow
    options.forEach(({ value, label }) => {
      optionsMap.set(value, label)
    })

    if (isArray(value)) {
      const labels = value.map((v) => optionsMap.get(v)).filter((v) => !isNil(v))
      text = labels.length ? labels.join(',') : NULL_TEXT
    } else {
      text = defaultTo(optionsMap.get(value), NULL_TEXT)
    }
  }
  return text ?? NULL_TEXT
}

DictSelect.Text = OptionText

export { DictSelect }
