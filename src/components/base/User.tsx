import React from 'react'
import { DownOutlined, ExportOutlined } from '@ant-design/icons'
import { Dropdown, Menu } from 'antd'
import { useNavigate } from 'react-router-dom'
import uc from '@galaxy/uc'
import UserAvatar from '@/components/base/UserAvatar'
import userService from '@/mainApp/services/userService'
import { logout } from '@/mainApp/services/authService'

const dropMenu = (
  <Menu
    items={[
      {
        label: (
          <a
            style={{
              textAlign: 'center',
            }}
            onClick={() => {
              uc.logout().then(() => {
                logout()
              })
            }}
          >
            &nbsp;&nbsp;退出
          </a>
        ),
        key: 'logout',
        icon: <ExportOutlined />,
      },
    ]}
  />
)

const User = () => {
  const navigate = useNavigate()
  return (
    <Dropdown overlay={dropMenu} trigger={['hover']}>
      <div
        onClick={() => {
          navigate('/user')
        }}
        style={{
          display: 'inline-block',
          padding: '0 10px',
          cursor: 'pointer',
        }}
      >
        <UserAvatar
          username={userService.userInfo.username}
          userNumber={userService.userInfo.userNumber}
        />
        <DownOutlined />
      </div>
    </Dropdown>
  )
}

export default User
