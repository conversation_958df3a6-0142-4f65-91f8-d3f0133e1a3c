import React from 'react'
import { Avatar } from 'antd'
import { UserOutlined } from '@ant-design/icons'

type Iprops = {
  username: string
  userNumber: string
}

const UserAvatar: React.FC<Iprops> = (props) => {
  const { username, userNumber } = props
  return (
    <>
      <Avatar icon={<UserOutlined />} />
      <div
        style={{
          margin: '0 10px',
          minWidth: 100,
          display: 'inline-block',
        }}
      >
        {username && `${username}(${userNumber})`}
      </div>
    </>
  )
}

export default UserAvatar
