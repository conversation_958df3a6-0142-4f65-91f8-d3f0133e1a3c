import React from 'react'
import { Result, Button, Tabs } from 'antd'

import config from '@/config'

const { TabPane } = Tabs

const { baseRoute } = config

class ErrorBoundary extends React.Component<
  any,
  { hasError: boolean; stackMsg: string; errorMsg: string }
> {
  constructor(props) {
    super(props)
    this.state = {
      hasError: false,
      stackMsg: '',
      errorMsg: '',
    }
  }

  componentDidCatch(error, info) {
    this.setState({
      hasError: true,
      stackMsg: info.componentStack,
      errorMsg: error.stack,
    })
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          style={{
            minHeight: '100vh',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Result
            status="500"
            title="服务异常"
            subTitle="抱歉，我们的服务遇到了一点小问题，您可以稍后刷新重试或者返回首页"
            extra={[
              <Button
                key={1}
                style={{
                  marginRight: 20,
                }}
                onClick={() => window.location.reload()}
              >
                刷新页面
              </Button>,
              <Button key={2} type="primary">
                <a href={`${baseRoute}/`}>返回首页</a>
              </Button>,
            ]}
          >
            <Tabs defaultActiveKey="1">
              <TabPane tab="错误信息" key="1">
                <pre>{this.state.errorMsg}</pre>
              </TabPane>
              <TabPane tab="堆栈信息" key="2">
                <pre>{this.state.stackMsg}</pre>
              </TabPane>
            </Tabs>
          </Result>
        </div>
      )
      // Note: 也可以在出错的component处展示出错信息，返回自定义的结果。
    }

    if (React.isValidElement(this.props.children)) {
      return this.props.children
    }
    return null
  }
}

export default ErrorBoundary
