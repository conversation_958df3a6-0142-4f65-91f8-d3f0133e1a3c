## ConfirmModal 组件说明

> 以 `antd Modal（4.20.5）` 组件进行封装。
>
> 屏蔽原组件中的 `title` 与 `footer` 部分，进行自定义实现。`footer` 部分相关 `props` 已进行绑定与透传，可直接传入，与原生 `Modal` 一致。`title` 属性替换为 `header` 属性。

### 自定义属性说明

> 以下都为可选参数

#### className

- 类型: `string`
- 说明: 透传给 `Modal`（组件最外层元素）的类名

#### header

- 类型: `React.ReactNode`

- 说明: 替代 `header` 部分的自定义内容

#### titleText

- 类型: `string`
- 说明：不传入 `header` 属性的情况下，自定义的标题文字
- 默认值：'默认标题'

#### titleIcon

- 类型: `React.ReactNode`

- 说明: 不传入 `header` 属性的情况下，自定义的标题图标

- 默认值: `<InfoCircleFilled className={styles.info} />`

  ```css
  .info {
    color: #faad14;
    font-size: 28px;
  }
  ```

### **_其他属性与原生 Modal 一致_**
