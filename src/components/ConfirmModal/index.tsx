import React from 'react'
import { Modal, ModalProps, Button, ButtonProps } from 'antd'
import { InfoCircleFilled } from '@ant-design/icons'
import classNames from 'classnames'

import styles from './style.module.less'

interface IProps {
  titleText?: string
  className?: string
  header?: React.ReactNode
  titleIcon?: React.ReactNode
}

const ConfirmModal: React.FC<IProps & Omit<ModalProps, 'title'>> = ({
  className,
  open,
  header,
  footer,
  onOk,
  onCancel,
  titleIcon,
  okButtonProps,
  cancelButtonProps,
  okType = 'primary',
  okText = '确定',
  cancelText = '取消',
  titleText = '默认标题',
  children,
  ...props
}) => {
  type LegacyButtonType = Exclude<typeof okType, undefined>

  /**
   *  antd 中 Modal.okType 实现的类型为 LegacyButtonType 即 { ButtonProps & ’danger' }
   *  如果直接将 okType 赋值给 Button.type 会因为 'danger' 导致类型校验失败
   *  需要通过 convertLegacyProps 进行 props 转换再将其扩展传入 Button
   *  这也是 Modal 中实现的方式
   */
  const convertLegacyProps = (type?: LegacyButtonType): ButtonProps => {
    if (type === 'danger') {
      return {
        danger: true,
      }
    }
    return {
      type,
    }
  }

  return (
    <Modal
      className={classNames(styles.modalWrapper, className)}
      footer={null}
      title={null}
      open={open}
      onCancel={onCancel}
      {...props}
    >
      {header || (
        <div className={styles.header}>
          <div className={styles.icon}>
            {titleIcon || <InfoCircleFilled className={styles.info} rev={undefined} />}
          </div>
          {titleText}
        </div>
      )}
      {children && <div className={styles.content}>{children}</div>}
      {footer === undefined ? (
        <div className={styles.footer}>
          <Button className={styles.cancelBtn} {...cancelButtonProps} onClick={onCancel}>
            {cancelText}
          </Button>
          <Button {...okButtonProps} {...convertLegacyProps(okType)} onClick={onOk}>
            {okText}
          </Button>
        </div>
      ) : (
        footer
      )}
    </Modal>
  )
}

export default ConfirmModal
