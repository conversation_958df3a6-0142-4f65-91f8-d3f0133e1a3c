import React from 'react'
import { Modal, ModalProps } from 'antd'
import classNames from 'classnames'
import styles from './index.less'

type CustomModalProps = ModalProps

const CustomModal = (props: CustomModalProps) => {
  const { children, className, footer = null, ...rest } = props
  return (
    <Modal
      className={classNames(
        styles.customModal,
        className,
        footer ? styles.customModalWithFooter : undefined,
      )}
      footer={footer}
      {...rest}
    >
      {children}
    </Modal>
  )
}

export default CustomModal
