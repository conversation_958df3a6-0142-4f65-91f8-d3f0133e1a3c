import React from 'react'
import { find } from 'lodash-es'
import type { PermissionItem } from '../permissionDropdown'

export interface IProps {
  children: React.ReactElement
  code: string
  permissions: PermissionItem[]
  disabledAction?: boolean
}

/**
 * permissions: [{ code: 'editProject', hasPermission: true }], 直接将 record 中的 permissions 属性传入
 * code: 对应操作项的code
 * disabledAction: 没有权限时是否显示操作项，显示的话会默认给元素增加 disabled 属性
 */
const PermissionAction: React.FC<IProps> = function (props) {
  const { code, permissions, children, disabledAction = true } = props

  // children 有可能里面使用了条件语句导致是空的
  if (!children) {
    return null
  }

  const node = React.cloneElement(children, {
    disabled: true,
  })

  if (find(permissions, ['code', code])?.hasPermission !== true) {
    if (!disabledAction) {
      return null
    }

    return node
  }

  return children
}

export default PermissionAction
