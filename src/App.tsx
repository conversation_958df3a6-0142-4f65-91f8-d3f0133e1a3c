// import 'babel-polyfill' // 兼容性代码
import React from 'react'
import 'normalize.css' // css样式重制
import 'moment/locale/zh-cn'
import 'dayjs/locale/zh-cn'
import dayjs from 'dayjs'
import ConfigProvider from '@galaxy/config-provider'
import { init as UserInit } from '@galaxy/user-selector'
import { init as ImportInit } from '@galaxy/async-task-component'
import { QueryClient, QueryClientProvider } from 'react-query'
import { ConfigProvider as AndConfigProvider } from 'antd'
import zhCN from 'antd/lib/locale/zh_CN'
// 注册 schema-form 提供的组件
import '@amazebird/antd-field'
import { init as UploadInit } from '@galaxy/upload'
// 未引入request会导致uc、rbac拦截器获取不到token
import '@/utils/request'
import config from '@/config'
import AppRouter from '@/router'
import ErrorBoundary from '@/components/base/ErrorBoundary'
import './libs/web-streams-polyfill.min'
import './galaxyConfig'
// Theme
import './themes/theme.less'
import './themes/style.less'
// 引入样式文件
import './themes/theme-main.less'
import './components/CustomFormComp'
import { LoaderContextDecorator } from './components/Loader/useLoading'
import { CLIENT_CODES } from './constants'

const { environment } = config

dayjs.locale('zh-cn')
ConfigProvider.config({
  env: environment,
})
UploadInit({ env: environment })

UserInit({
  env: environment,
})

ImportInit({
  env: environment,
})

AndConfigProvider.config({
  prefixCls: CLIENT_CODES.antdPrefixCls,
})

const queryClient = new QueryClient()

const AppContent = LoaderContextDecorator(() => {
  const container = document.getElementById('microRoot') as HTMLElement
  return (
    <AndConfigProvider
      locale={zhCN}
      getPopupContainer={() => container}
      prefixCls={CLIENT_CODES.antdPrefixCls}
    >
      <AppRouter />
    </AndConfigProvider>
  )
})

const APP = function () {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AppContent />
      </QueryClientProvider>
    </ErrorBoundary>
  )
}

export default APP
