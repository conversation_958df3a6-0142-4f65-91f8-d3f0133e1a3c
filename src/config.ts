import defaultConfig from '../config'
import { isMicroApp } from './constants/index'

const isMultiVersion = window.location.pathname.indexOf('/multi-version') > -1
const prefix = isMultiVersion ? '/multi-version' : ''
const suffix = isMultiVersion ? `/${process.env.VERSION || ''}` : ''

const config = {
  ...defaultConfig,
  baseRoute: `${prefix}${
    isMicroApp ? '/employee-relations' : '/dhr-employee-relations-webapp'
  }${suffix}`,
  openBaseRoute: `${prefix}/employee-relations${suffix}`, // window.open时项目路径
}
export default config
