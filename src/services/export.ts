import { TaskController } from '@galaxy/async-task-component'
import { OSS_CONFIG } from '@/constants'
import { uploadSdk } from './upload'

export type ExportResult = {
  success: boolean
  objectKey?: string
  data?: any
  errMsg?: string
}

class AsyncExportService {
  taskController = new TaskController()

  async export(taskType, params): Promise<ExportResult> {
    try {
      const data = await this.taskController.createTask({
        service: OSS_CONFIG.service,
        params: {
          // 任务参数，具体参数咨询服务端
          taskType,
          // 任务参数，具体参数咨询服务端
          taskModule: OSS_CONFIG.module,
          // 任务参数，具体参数咨询服务端
          taskArgs: params,
        },
      })
      if (data.status === 2 && data?.objectKey) {
        // 下载
        const { url: fileUrl } = await uploadSdk.download(data?.objectKey)
        window.open(fileUrl, '_blank')
        return Promise.resolve({
          success: true,
          objectKey: data?.objectKey,
          data,
        })
      }
      return Promise.resolve({
        success: false,
        data,
        errMsg: data.errorMessage || '数据导出失败',
      })
    } catch (err) {
      return Promise.resolve({
        success: false,
        errMsg: '数据导出失败',
      })
    }
  }
}

export const exportService = new AsyncExportService()
