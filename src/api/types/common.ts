/**
 * ==============================
 * DEPENDENCIES OF common
 * Generated BY pupudesigner
 * DON'T MODIFY OR REMOVE!!!!!!
 * ==============================
 */

export type JsonResponse<T0> = {
  /** code */
  code?: number
  /** data */
  data?: T0
  /** msg */
  msg?: string
}

export type PageResponse<T0> = {
  /** code */
  code?: number
  /** count */
  count?: number
  /** data */
  data?: Array<T0>
  /** msg */
  msg?: string
}

export type SecurityEventDTO = {
  /** 案发区域:BM_BZGSQY */
  accidentArea?: string
  /** 案发区域（OA）:BM_73 */
  accidentAreaOa?: string
  /** 案件内容 */
  accidentContent?: string
  /** 案发合同主体:BM_HTZT */
  accidentContractSubject?: string
  /** 案发合同主体名称 */
  accidentContractSubjectName?: string
  /** 案发合同类型:BM_VAA */
  accidentContractType?: string
  /** 案发部门名称 */
  accidentDepartmentId?: string
  /** 在司时长分类 */
  accidentDurationCategory?: string
  /** 案发在司时长 */
  accidentDurationInCompany?: string
  /** 事故性质:THE_ACCIDENT_NATURE */
  accidentNature?: string
  /** 事故性质名称 */
  accidentNatureName?: string
  /** 案发岗位名称 */
  accidentPositionId?: string
  /** 案发场景:BM_CRIME_SCENE */
  accidentScenario?: string
  /** 案发年月 */
  accidentYearMonth?: string
  /** 理赔到账日期 */
  claimPaidDate?: number
  /** 结案日期 */
  closeDate?: number
  /** 公司还款金额 */
  companyRepayAmount?: number
  /** 完整理赔材料邮寄日期 */
  completeClaimMaterialPostDate?: number
  /** 联系方式 */
  contact?: string
  /** 当前合同主体名称 */
  currentContractSubjectName?: string
  /** 当前合同类型:BM_VAA */
  currentContractType?: string
  /** 当前欠款 */
  currentDebt?: number
  /** 时间分类:TIME_CLASSIFICATION */
  dateCategory?: string
  /** 时间分类名称 */
  dateCategoryName?: string
  /** 配送订单号 */
  deliveryOrderNum?: string
  /** 部门全称 */
  departmentFullName?: string
  /** 四级部门 */
  departmentLevelFourth?: string
  /** 伤残等级 */
  disabilityLevel?: string
  /** 员工还款金额 */
  employeeRepayAmount?: number
  /** 员工总欠款金额 */
  employeeTotalDebt?: number
  /** 人员类别:BM_KK */
  employmentState?: string
  /** 事件分类:EVENT_CLASSIFICATION */
  eventCategory?: string
  /** 事件分类名称 */
  eventCategoryName?: string
  /** 案件系数:BM_AJXS */
  eventCoefficient?: string
  /** 案件系数变更原因 */
  eventCoefficientChangeReason?: string
  /** 事件等级名称 */
  eventLevelName?: string
  /** 事件发生地点 */
  eventLocation?: string
  /** 跟进周期分类:BM_PeriodicClass */
  followCycleCategory?: string
  /** 跟进时长 */
  followPeriod?: string
  /** 跟进时长分类 */
  followPeriodCategory?: string
  /** 事件id */
  id?: number
  /** 受伤人员情况:BM_INJURYSTATUS */
  injuredPersonnelCondition?: string
  /** 保险还款金额 */
  insuranceRepayAmount?: number
  /** 申报险种 */
  insureType?: string
  /** 是否申请劳动能力鉴定 */
  isApplyLaborAbilityIdentification?: boolean
  /** 是否申请工伤待遇赔偿 */
  isApplyWorkInjuryCompensation?: boolean
  /** 是否申请认定工伤 */
  isApplyWorkInjuryRecognition?: boolean
  /** 是否借支 */
  isBorrow?: boolean
  /** 出险员工是否处罚 */
  isStaffPenalty?: boolean
  /** 是否挂起 */
  isSuspend?: boolean
  /** 是否涉及第三方 */
  isThirdPartyInvolved?: boolean
  /** 是否认定为工伤 */
  isWorkInjury?: boolean
  /** 不处罚原因:BM_NoPunishReason */
  noPenaltyReason?: string
  /** 不处罚原因详情 */
  noPenaltyReasonDetails?: string
  /** 未实行奖惩原因 */
  notImplementedRewardPunishReason?: string
  /** 危机管理单号 */
  oaNo?: string
  /** 处罚类型:TYPE_OF_PENALTY */
  penaltyType?: string
  /** 处罚类型名称 */
  penaltyTypeName?: string
  /** 岗位 */
  positionId?: number
  /** 岗位类型划分:BM_POSTTYPE */
  positionType?: string
  /** 进度 */
  process?: string
  /** 进度说明 */
  processRemark?: string
  /** 返还金额 */
  recoverAmount?: number
  /** 返还方式 */
  recoverMethod?: string
  /** 已返还金额 */
  recoveredAmount?: number
  /** 备注 */
  remark?: string
  /** 备注1 */
  remarkOther?: string
  /** 备注2 */
  remarkOtherTwo?: string
  /** 还款进度 */
  repayProgress?: string
  /** 申报单位 */
  reportUnit?: string
  /** 责任划分:BM_RESPONSIBILITY */
  responsibilityAllocation?: string
  /** 奖惩类别 */
  rewardPunishCategory?: string
  /** 奖惩日期 */
  rewardPunishDate?: number
  /** 奖惩进度 */
  rewardPunishProgress?: string
  /** 奖惩类型 */
  rewardPunishType?: string
  /** 员工受伤类型:BM_WOUNDTYPE */
  staffInjuryType?: string
  /** 开始返还日期 */
  startRecoverDate?: number
  /** 挂起日期 */
  suspendDate?: number
  /** 挂起原因 */
  suspendReason?: string
  /** 第三方年龄 */
  thirdPartyAge?: string
  /** 第三方联系方式 */
  thirdPartyContact?: string
  /** 第三方受伤类型:BM_WOUNDTYPE */
  thirdPartyInjuryType?: string
  /** 第三方姓名 */
  thirdPartyName?: string
  /** 事故日期 */
  timeAccident?: number
  /** 创建时间 */
  timeCreate?: number
  /** 事件生成日期 */
  timeEventCreated?: number
  /** 入职日期 */
  timeEntry?: number
  /** 离职日期 */
  timeLeave?: number
  /** 更新时间 */
  timeUpdate?: number
  /** 总借支金额 */
  totalBorrowAmount?: number
  /** 未结案原因 */
  unclosedReason?: string
  /** 创建人工号 */
  userIdCreate?: string
  /** 责任店长 */
  userIdResponsibleManager?: string
  /** 更新人工号 */
  userIdUpdate?: string
  /** 员工姓名 */
  userName?: string
  /** 创建人姓名 */
  userNameCreate?: string
  /** 更新人姓名 */
  userNameUpdate?: string
  /** 员工工号 */
  userNum?: string
  /** 天气:THE_WEATHER */
  weather?: string
  /** 天气名称 */
  weatherName?: string
  /** 撤案原因 */
  withdrawReason?: string
  /** 撤案日期 */
  withdrawDate?: number
  /** 支付日期 */
  paymentDate?: number
  /** 附件 */
  attachment: string
}
