import { IResponse } from '@/types'
import services from '@/utils/request'

/** 新增借款审批 */
export const createSecurityLoan = (data: any): Promise<IResponse<any>> =>
  services.post(`/admin/staff_relation/v2/third_party_loan`, data)

/** 根据id查询借款详情 */
export const getSecurityLoanById = (id: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/v2/third_party_loan/${id}`)

/** 根据id修改借款详情 */
export const modifySecurityLoanById = (id: string, data: any): Promise<IResponse<any>> =>
  services.put(`/admin/staff_relation/v2/third_party_loan/${id}`, data)

/** 删除借款审批 */
export const deleteSecurityLoanById = (id: string): Promise<IResponse<any>> =>
  services.delete(`/admin/staff_relation/v2/third_party_loan/${id}`)

/** 保存借款审批草稿 */
export const saveSecurityLoanDraft = (data: any): Promise<IResponse<any>> =>
  services.post(`/admin/staff_relation/v2/third_party_loan/draft`, data)

/** 创建借款审批前检查 */
export const createSecurityLoanCheck = (eventId: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/v2/third_party_loan/creation_check?event_id=${eventId}`)

/** 根据id查询借款详情 OA */
export const getSecurityLoanOAById = (id: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/partner/v2/third_party_loan/${id}`)
