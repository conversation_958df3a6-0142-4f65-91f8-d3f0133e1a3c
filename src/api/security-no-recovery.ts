import { IResponse } from '@/types'
import services from '@/utils/request'

/** 41012_56451203:[SERVER, USER]:新增不追偿申请 */
export const createNoRecovery = (data: any): Promise<IResponse<any>> =>
  services.post(`/admin/staff_relation/v2/non_recovery_applications`, data)

/** 41012_56451201:[SERVER, USER]:根据id查询详情 */
export const getNoRecoveryById = (id: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/v2/non_recovery_applications/${id}`)

/** 41012_56451202:[SERVER, USER]:根据id修改详情 */
export const modifyNoRecoveryById = (id: string, data: any): Promise<IResponse<any>> =>
  services.put(`/admin/staff_relation/v2/non_recovery_applications/${id}`, data)

/** 41012_56451205:[SERVER, USER]:删除不追偿申请 */
export const deleteNoRecoveryById = (id: string): Promise<IResponse<any>> =>
  services.delete(`/admin/staff_relation/v2/non_recovery_applications/${id}`)

/** 41012_56451204:[SERVER, USER]:保存不追偿申请草稿 */
export const saveNoRecoveryDraft = (data: any): Promise<IResponse<any>> =>
  services.post(`/admin/staff_relation/v2/non_recovery_applications/draft`, data)

/** 41012_56451206:[SERVER, USER]:创建不追偿申请前检查 */
export const createNoRecoveryCheck = (eventId: string): Promise<IResponse<any>> =>
  services.get(
    `/admin/staff_relation/v2/non_recovery_applications/creation_check?event_id=${eventId}`,
  )

/** 根据id查询详情 OA */
export const getNoRecoveryOAById = (id: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/partner/v2/non_recovery_applications/${id}`)
