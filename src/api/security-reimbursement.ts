import { IResponse } from '@/types'
import services from '@/utils/request'

// /admin/staff_relation/v2/payment_approvals

/** 41012_46451203:[SERVER, USER]:新增回款审批 */
export const createSecurityReimbursement = (data: any): Promise<IResponse<any>> =>
  services.post(`/admin/staff_relation/v2/payment_approvals`, data)

/** 41012_46451201:[SERVER, USER]:根据id查询详情 */
export const getSecurityReimbursementById = (id: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/v2/payment_approvals/${id}`)

/** 41012_46451202:[SERVER, USER]:根据id修改详情 */
export const modifySecurityReimbursementById = (id: string, data: any): Promise<IResponse<any>> =>
  services.put(`/admin/staff_relation/v2/payment_approvals/${id}`, data)

/** 41012_46451205:[SERVER, USER]:删除回款审批 */
export const deleteSecurityReimbursementById = (id: string): Promise<IResponse<any>> =>
  services.delete(`/admin/staff_relation/v2/payment_approvals/${id}`)

/** 41012_46451204:[SERVER, USER]:保存回款审批草稿 */
export const saveSecurityReimbursementDraft = (data: any): Promise<IResponse<any>> =>
  services.post(`/admin/staff_relation/v2/payment_approvals/draft`, data)

/** 41012_46451206:[SERVER, USER]:创建回款审批前检查 */
export const createSecurityReimbursementCheck = (eventId: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/v2/payment_approvals/creation_check?event_id=${eventId}`)

/** 根据id查询详情 OA */
export const getSecurityReimbursementOAById = (id: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/partner/v2/payment_approvals/${id}`)
