import { IPaginationResponse, IResponse } from '@/types'
import services from '@/utils/request'

export type IdParams = {
  /** id */
  id?: string
}
/** 104:发起OA付款单 */
export const addOaDisputePayment = ({ id }: IdParams): Promise<IResponse<boolean>> =>
  services.post(`/admin/staff_relation/v2/employment_dispute/payment/${id}?`)

export type IEmploymentDisputeParams = {
  /** 案发区域:BM_BZGSQY */
  accidentArea?: string
  /** 案发合同主体 */
  accidentContractSubject?: string
  /** 所属部门 */
  departmentId?: string
  /** 进度:BX_AJJD */
  process?: string
  /** 案发结束时间 */
  timeAccidentEnd?: number
  /** 案发开始时间 */
  timeAccidentStart?: number
  /** 员工工号 */
  userNum?: string
  /** 页码 */
  page?: number
  /** 分页数量，最大200 */
  size?: number
  /** 排序，选填，格式:http://ip:port?sort=id,asc&sort=user_name,desc */
  sort?: string
}
/**
 * 102:分页查询
 * @param params
 * @returns
 */
export const getEmploymentDisputeList = (
  params: IEmploymentDisputeParams,
): Promise<IPaginationResponse<any>> =>
  services.get('/admin/staff_relation/v2/employment_dispute/query', params)

/**
 * 101:根据id查询详情
 */
export const getEmploymentDisputeById = ({ id }: IdParams): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/v2/employment_dispute/${id}`)

/**
 * 103:根据id修改详情
 * @param param
 * @returns
 */
export const modifyEmploymentDisputeById = ({ id, ...data }): Promise<IResponse<boolean>> =>
  services.put(`/admin/staff_relation/v2/employment_dispute/${id}?`, data)

/**
 * 105:查询案发合同主体列表
 * @param options
 * @returns
 */
export const getDisputeContractSubject = (): Promise<IResponse<Array<string>>> =>
  services.get(`/admin/staff_relation/v2/employment_dispute/contract_subject`)

/**
 * 根据id查询用工争议详情 OA
 */
export const getEmploymentDisputeOAById = ({ id }: IdParams): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/partner/v2/employment_dispute/${id}`)
