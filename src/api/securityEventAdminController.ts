/**
 * =============================================
 * DEPENDENCIES OF securityEventAdminController
 * Generated BY pupudesigner
 * DON'T MODIFY OR REMOVE!!!!!!
 * =============================================
 */

import http from '@/utils/request'
import { JsonResponse, SecurityEventDTO, PageResponse } from './types/common'
import { SecurityEventUpdateRequest } from './types/securityEventAdminController'

export type GetDminStaffRelationV1SecurityEventByIdParams = {
  /** id */
  id: number
}
/** 201:根据id查询详情 */
export const getDminStaffRelationV1SecurityEventById = (
  { id }: GetDminStaffRelationV1SecurityEventByIdParams,
  options?: any,
): Promise<JsonResponse<SecurityEventDTO>> =>
  http.get(`/admin/staff_relation/v2/security_event/${id}?`, undefined, options)

export type GetDminStaffRelationV1SecurityEventQueryParams = {
  /** 案发区域:BM_BZGSQY */
  accidentArea?: string
  /** 案发合同主体 */
  accidentContractSubject?: string
  /** 所属部门 */
  departmentId?: string
  /** 进度:BX_AJJD */
  process?: string
  /** 案发结束时间 */
  timeAccidentEnd?: number
  /** 案发开始时间 */
  timeAccidentStart?: number
  /** 员工工号 */
  userNum?: string
  /** 页码 */
  page?: number
  /** 分页数量，最大200 */
  size?: number
  /** 排序，选填，格式:http://ip:port?sort=id,asc&sort=user_name,desc */
  sort?: string
}
/** 202:分页查询 */
export const getDminStaffRelationV1SecurityEventQuery = (
  data: GetDminStaffRelationV1SecurityEventQueryParams,
  options?: any,
): Promise<PageResponse<SecurityEventDTO>> =>
  http.get(`/admin/staff_relation/v2/security_event/query?`, data, options)

export type PutDminStaffRelationV1SecurityEventByIdParams = {
  /** id */
  id?: string
}
/** 203:根据id修改详情 */
export const putDminStaffRelationV1SecurityEventById = (
  { id, ...data }: PutDminStaffRelationV1SecurityEventByIdParams & SecurityEventUpdateRequest,
  options?: any,
): Promise<JsonResponse<void>> =>
  http.put(`/admin/staff_relation/v2/security_event/${id}?`, data, options)

export const getcontractSubject = (options?: any): Promise<JsonResponse<SecurityEventDTO>> =>
  http.get(`/admin/staff_relation/v2/security_event/contract_subject/?`, options)

/** 安全事件结案 */
export const postTermination = ({ id }: { id: number }, data: any): Promise<JsonResponse<void>> =>
  http.post(`/admin/staff_relation/v2/security_event/termination/${id}`, data)

/** 安全事件撤案 */
export const postRevocation = ({ id }: { id: number }, data: any): Promise<JsonResponse<void>> =>
  http.post(`/admin/staff_relation/v2/security_event/revocation/${id}`, data)

/** 安全事件取消撤案 */
export const postRevocationCancellation = (id: number): Promise<JsonResponse<void>> =>
  http.post(`/admin/staff_relation/v2/security_event/revocation_cancellation/${id}`)

/** 升级至安全争议 */
export const putSafetyDispute = (id: number): Promise<JsonResponse<void>> =>
  http.put(`/admin/staff_relation/v2/security_event/safety_dispute/${id}`)
