import { IResponse } from '@/types'
import services from '@/utils/request'

/** 41012_36451201:[SERVER, USER]:根据id查询详情 */
export const getCompensationById = (id: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/v2/compensation_amount_requests/${id}`)

/** 41012_36451203:[SERVER, USER]:新增赔偿额度申请 */
export const createCompensation = (data: any): Promise<IResponse<any>> =>
  services.post(`/admin/staff_relation/v2/compensation_amount_requests`, data)

/** 41012_36451202:[SERVER, USER]:根据id修改详情  */
export const modifyCompensationById = (id: string, data: any): Promise<IResponse<any>> =>
  services.put(`/admin/staff_relation/v2/compensation_amount_requests/${id}`, data)

/** 41012_36451205:[SERVER, USER]:删除赔偿额度申请 */
export const deleteCompensationById = (id: string): Promise<IResponse<any>> =>
  services.delete(`/admin/staff_relation/v2/compensation_amount_requests/${id}`)

/** 41012_36451204:[SERVER, USER]:保存赔偿额度申请草稿 */
export const saveCompensationDraft = (data: any): Promise<IResponse<any>> =>
  services.post(`/admin/staff_relation/v2/compensation_amount_requests/draft`, data)

/** 41012_36451206:[SERVER, USER]:创建赔偿额度申请前检查 */
export const createCompensationCheck = ({
  eventId,
  source,
}: {
  eventId: string
  source: number
}): Promise<IResponse<any>> =>
  services.get(
    `/admin/staff_relation/v2/compensation_amount_requests/creation_check?event_id=${eventId}&source=${source}`,
  )

/** 41012_36451207:[SERVER, USER]:获取员工费用系数 */
export const getStaffFeeCoefficient = (responsibilityAllocation: string): Promise<IResponse<any>> =>
  services.get(
    `/admin/staff_relation/v2/compensation_amount_requests/expense_coefficient?responsibility_allocation=${responsibilityAllocation}`,
  )

/** 根据id查询详情 OA */
export const getCompensationOAById = (id: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/partner/v2/compensation_amount_requests/${id}`)
