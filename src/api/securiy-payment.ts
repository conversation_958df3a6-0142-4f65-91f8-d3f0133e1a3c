import { IResponse } from '@/types'
import services from '@/utils/request'

/** 新增付款审批 */
export const createSecurityPayment = (data: any): Promise<IResponse<any>> =>
  services.post(`/admin/staff_relation/v2/third_party_payment`, data)

/** 根据id查询付款详情 */
export const getSecurityPaymentById = (id: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/v2/third_party_payment/${id}`)

/** 根据id修改付款详情 */
export const modifySecurityPaymentById = (id: string, data: any): Promise<IResponse<any>> =>
  services.put(`/admin/staff_relation/v2/third_party_payment/${id}`, data)

/** 删除付款审批 */
export const deleteSecurityPaymentById = (id: string): Promise<IResponse<any>> =>
  services.delete(`/admin/staff_relation/v2/third_party_payment/${id}`)

/** 保存付款审批草稿 */
export const saveSecurityPaymentDraft = (data: any): Promise<IResponse<any>> =>
  services.post(`/admin/staff_relation/v2/third_party_payment/draft`, data)

/** 创建付款审批前检查 */
export const createSecurityPaymentCheck = (eventId: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/v2/third_party_payment/creation_check?event_id=${eventId}`)

/** 根据id查询付款详情 OA */
export const getSecurityPaymentOAById = (id: string): Promise<IResponse<any>> =>
  services.get(`/admin/staff_relation/partner/v2/third_party_payment/${id}`)
