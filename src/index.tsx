// import 'core-js/stable';
import './publicPath'
import 'regenerator-runtime/runtime'
import React from 'react'
import RouteFactory from '@/router/RouteFactory'
import { employeeRelationRoute } from '@/router/route/mainRoutes'
import { createRoot } from 'react-dom/client'
import App from './App'

let root

function render(props) {
  const { container } = props
  const containerDom = container
    ? container.querySelector('#microRoot')
    : document.querySelector('#microRoot')
  root = createRoot(containerDom)
  root.render(<App />)
}

if (!window.__POWERED_BY_QIANKUN__) {
  render({})
}

// eslint-disable-next-line @typescript-eslint/no-empty-function
export async function bootstrap() {}

const updateStateFromMainApp = (props, subAppName) => {
  const routeMap = {
    'employee-relationsRoute': employeeRelationRoute,
  }

  const routes = RouteFactory.joinBaseName([routeMap[`${subAppName}Route`]])
  props.registerRoute(routes)
}

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props) {
  const subAppName = props.name

  props.onGlobalStateChange((params) => updateStateFromMainApp(params, subAppName), true)
  render(props)
}

export async function unmount() {
  root.unmount()
}

// eslint-disable-next-line @typescript-eslint/no-empty-function
export async function update() {}
