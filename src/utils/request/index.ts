import axios, { dataAssignmentResponse } from '@galaxy/business-request'
import { authHeaderRequestInterceptor } from '@galaxy/uc'
import { rbac<PERSON>eaderInterceptor } from '@galaxy/rbac'
import { message } from 'antd'
import configObj from '@/config'
import { logout } from '@/mainApp/services/authService'
import { apmResponse, apmResponseError } from './apmResponse'

axios.defaults.timeout = 60000
axios.interceptors.response.use(apmResponse, apmResponseError)
axios.interceptors.request.use(rbacHeaderInterceptor)
axios.interceptors.request.use(authHeaderRequestInterceptor)

const req = axios.inheritCreate()
req.interceptors.response.use(dataAssignmentResponse)

const { environment, serverUrl, baseRoute } = configObj

const request = (config) => {
  const matchRegExp = /http|https/
  const localUrl = `${window.location.origin}/mock`
  const { isMock = false } = config
  // 仅允许本地环境使用Mock功能；
  const SERVER_URL = environment === 'local' && isMock ? localUrl : serverUrl
  const url = `${matchRegExp.test(config.url) ? '' : SERVER_URL}${config.url}${
    config.url.indexOf('?') > -1 ? '&t=' : '?t='
  }${Date.now()}`
  return {
    ...config,
    url,
  }
}

const responseError = (error) => {
  const { status, msg, body, code } = error
  if (body?.config?.silent) {
    return Promise.reject(error)
  }
  if (status === 401) {
    message.error('认证失败，重新登录跳转中...', 3)
    logout()
  } else if (status === 403) {
    window.location.href = `${baseRoute}/403`
  } else if (code === 'DATA_STATE_CHANGE') {
    return Promise.reject(error)
  } else {
    message.error(msg, 3)
  }

  return Promise.reject(error)
}

req.interceptors.request.use(request)
axios.interceptors.response.use(undefined, responseError, 'globalMessage')

export default req
