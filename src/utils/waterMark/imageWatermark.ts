interface ImageWatermarkOptions {
  src: string
  watermarkText: string
  quality?: number // 图片质量，0-1之间，默认1.0
}

interface ImageWatermarkResult {
  dataUrl: string
  blob: Blob
  error?: string
}

// 最大尺寸限制（防止内存溢出）
const MAX_WIDTH = 2000
const MAX_HEIGHT = 2000

/**
 * 在Canvas上绘制水印
 * @param ctx Canvas 2D context
 * @param watermarkText 水印文本
 * @param width 画布宽度
 * @param height 画布高度
 */
const drawWatermarkOnCanvas = (
  ctx: CanvasRenderingContext2D,
  watermarkText: string,
  width: number,
  height: number,
) => {
  // 根据图片尺寸自适应字体大小
  const baseFontSize = Math.min(width, height) / 50 // 基础字体大小
  const fontSize = Math.max(12, Math.min(24, baseFontSize)) // 限制在12-24px之间
  const angle = (23 * Math.PI) / 180
  const text = watermarkText || '水印文字'

  ctx.save()
  ctx.fillStyle = 'rgba(0, 0, 0, 0.1)' // 设置透明度
  ctx.font = `${fontSize}px Noto Sans SC, Arial, sans-serif`
  ctx.textAlign = 'center'

  // 计算文本宽度
  const textWidth = ctx.measureText(text).width
  // 根据图片尺寸调整水印间距
  const diagonalStep = Math.max(textWidth * 1.2, fontSize * 4)

  // 根据图片尺寸调整起始位置和步长
  const startX = Math.min(width * 0.1, 100)
  const startY = Math.min(height * 0.1, 80)
  const stepX = diagonalStep * 1.8
  const stepY = diagonalStep * 0.8

  // 绘制水印，覆盖整个图片区域
  for (let y = startY; y < height + diagonalStep; y += stepY) {
    for (let x = startX; x < width + diagonalStep; x += stepX) {
      ctx.save()
      ctx.translate(x, y)
      ctx.rotate(-angle)
      ctx.fillText(text, 0, 0)
      ctx.restore()
    }
  }

  ctx.restore()
}

/**
 * 处理图片水印并返回结果
 * @param options 配置选项
 * @returns Promise<ImageWatermarkResult>
 */
export const processImageWatermark = async (
  options: ImageWatermarkOptions,
): Promise<ImageWatermarkResult> => {
  const { src, watermarkText, quality = 1.0 } = options

  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      resolve({
        dataUrl: '',
        blob: new Blob(),
        error: '无法创建canvas context',
      })
      return
    }

    const img = new Image()
    img.crossOrigin = 'anonymous' // 处理跨域

    img.onload = () => {
      try {
        // 使用原始图片尺寸，但限制最大尺寸防止内存溢出
        let canvasWidth = img.width
        let canvasHeight = img.height

        // 如果图片过大，按比例缩放
        if (canvasWidth > MAX_WIDTH || canvasHeight > MAX_HEIGHT) {
          const scale = Math.min(MAX_WIDTH / canvasWidth, MAX_HEIGHT / canvasHeight)
          canvasWidth = Math.floor(canvasWidth * scale)
          canvasHeight = Math.floor(canvasHeight * scale)
        }

        // 设置 canvas 尺寸为图片实际尺寸
        canvas.width = canvasWidth
        canvas.height = canvasHeight

        // 绘制原始图片（填满整个canvas）
        ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight)

        // 绘制水印
        drawWatermarkOnCanvas(ctx, watermarkText, canvasWidth, canvasHeight)

        // 导出为 DataURL
        const dataUrl = canvas.toDataURL('image/png', quality)

        // 导出为 Blob
        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve({
                dataUrl,
                blob,
              })
            } else {
              resolve({
                dataUrl: '',
                blob: new Blob(),
                error: '生成blob失败',
              })
            }
          },
          'image/png',
          quality,
        )
      } catch (error) {
        resolve({
          dataUrl: '',
          blob: new Blob(),
          error: error instanceof Error ? error.message : '处理图片失败',
        })
      }
    }

    img.onerror = () => {
      resolve({
        dataUrl: '',
        blob: new Blob(),
        error: '图片加载失败',
      })
    }

    img.src = src
  })
}

/**
 * 简化版本的图片水印处理（直接返回URL）
 * @param src 图片源
 * @param watermarkText 水印文本
 * @returns Promise<string> 返回处理后的图片URL
 */
export const addWatermarkToImage = async (src: string, watermarkText: string): Promise<string> => {
  try {
    const result = await processImageWatermark({
      src,
      watermarkText,
    })

    if (result.error) {
      throw new Error(result.error)
    }

    // 创建blob URL
    const blobUrl = URL.createObjectURL(result.blob)
    return blobUrl
  } catch (error) {
    console.error('图片水印处理失败:', error)
    throw error
  }
}

/**
 * 批量处理图片水印
 * @param images 图片配置数组
 * @returns Promise<string[]> 返回处理后的图片URL数组
 */
export const batchProcessImageWatermarks = async (
  images: Array<{ src: string; watermarkText: string }>,
): Promise<string[]> => {
  const promises = images.map(async (imageConfig) => {
    try {
      return addWatermarkToImage(imageConfig.src, imageConfig.watermarkText)
    } catch (error) {
      console.error(`处理图片失败: ${imageConfig.src}`, error)
      return '' // 返回空字符串表示处理失败
    }
  })

  return Promise.all(promises)
}

// 导出类型定义
export type { ImageWatermarkOptions, ImageWatermarkResult }
