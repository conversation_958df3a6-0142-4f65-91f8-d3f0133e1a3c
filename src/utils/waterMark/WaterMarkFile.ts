import { getToken } from '@galaxy/uc'
import { addWatermarkToPDF } from './pdfWatermark'
import { addWatermarkToImage } from './imageWatermark'

interface ProcessFileOptions {
  fileUrl: string
  uuid: string
  fileName: string
}

interface ProcessFileResult {
  processedUrl: string
  isImage: boolean
  error?: string
}

/**
 * 清理blob URL，释放内存
 * @param url blob URL
 */
export const cleanupBlobUrl = (url: string) => {
  if (url && url.startsWith('blob:')) {
    URL.revokeObjectURL(url)
  }
}

function formatDate(cTime, cFormat) {
  // 判空
  if (arguments.length === 0 || typeof cTime === 'undefined' || !cTime) {
    return null
  }
  let date = cTime
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  // 如果不是Date对象
  if (typeof date !== 'object') {
    if (typeof date === 'string') {
      if (/^[0-9]+$/.test(date)) {
        // support "1548221490638"
        date = Number(date)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        date = date.replace(/-/gm, '/')
      }
    }

    if (typeof date === 'number' && date.toString().length === 10) {
      date *= 1000
    }
    date = new Date(date)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const timeStr = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return timeStr
}

export const getUserInfo = () => {
  const tk = getToken()?.accessToken || ''
  if (tk) {
    const strings = tk.split('.') // 截取token，获取载体
    const userInfo = JSON.parse(
      decodeURIComponent(escape(window.atob(strings[1].replace(/-/g, '+').replace(/_/g, '/')))),
    ) // 解析，需要吧‘_’,'-'进行转换否则会无法解析
    // console.log('当前的userInfo', userInfo)
    // setUserInfo(userInfo || {});
    return userInfo
  }
  return null
}

/**
 * 生成水印文本（基于你原来的逻辑）
 * @param userNum 用户工号
 * @param userName 用户名
 * @param inputDate 日期，默认当前时间
 * @returns 水印文本
 */
const generateWatermark = () => {
  const userInfo = getUserInfo()
  const currentUserNum = userInfo?.pp_uc?.sub || userInfo?.ucUserName
  const currentUserName = userInfo?.pp_uc?.name || userInfo?.name
  return `${currentUserNum}-${currentUserName}-${formatDate(new Date(), '{y}.{m}.{d} {h}:{i}')}`
}

/**
 * 处理文件水印 - 统一处理图片和PDF文件
 * @param options 配置选项
 * @returns 处理结果
 */
export const processFileWatermark = async (
  options: ProcessFileOptions,
): Promise<ProcessFileResult> => {
  const { fileUrl, fileName } = options

  try {
    // 判断文件类型
    const isImage = !fileName.endsWith('.pdf')

    // 处理协议头，保持和当前页面一致
    const protocol = window.location.protocol
    const normalizedUrl = fileUrl.startsWith(protocol)
      ? fileUrl
      : fileUrl.replace(/^https?:/, protocol)

    // 如果是图片，使用图片水印处理
    if (isImage) {
      const watermarkedUrl = await addWatermarkToImage(normalizedUrl, generateWatermark())

      return {
        processedUrl: watermarkedUrl,
        isImage: true,
      }
    }

    // PDF处理
    const response = await fetch(normalizedUrl)
    if (!response.ok) {
      throw new Error(`文件加载失败: ${response.status} ${response.statusText}`)
    }

    const buffer = await response.arrayBuffer()

    // 添加水印
    const pdfBytes = await addWatermarkToPDF({
      fileBytes: new Uint8Array(buffer),
      watermarkText: generateWatermark(),
      isImage: false,
      isWatermark: true,
    })

    // 创建blob URL
    const blob = new Blob([pdfBytes], { type: 'application/pdf' })
    const blobUrl = URL.createObjectURL(blob)

    return {
      processedUrl: blobUrl,
      isImage: false,
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '处理文件失败'
    return {
      processedUrl: '',
      isImage: false,
      error: errorMessage,
    }
  }
}
