import { PDFDocument, rgb, degrees, StandardFonts, PDFPage, PDFFont } from 'pdf-lib'
import fontkit from '@pdf-lib/fontkit'

interface PageSize {
  width: number
  height: number
}

function drawWatermark(page: PDFPage, text: string, font: PDFFont, pageSize: PageSize) {
  const fontSize = 12
  const color = rgb(0, 0, 0) // 黑色，透明度在绘制时设置
  const rotationAngle = 23 // 旋转角度

  const { width, height } = pageSize

  // 计算水印之间的间距
  const xGap = width / 2
  const yGap = height / 3

  // 在页面上绘制多个水印
  for (let x = 20; x < pageSize.width; x += yGap) {
    for (let y = 50; y < pageSize.height; y += xGap) {
      page.drawText(text, {
        x,
        y,
        size: fontSize,
        font,
        color,
        opacity: 0.1, // 设置透明度
        rotate: degrees(rotationAngle),
      })
    }
  }
}

async function createPDFFromImage(imageBytes: Uint8Array | string): Promise<PDFDocument> {
  const pdfDoc = await PDFDocument.create()
  let image
  try {
    // 尝试作为 JPEG 加载
    image = await pdfDoc.embedJpg(imageBytes)
  } catch (error) {
    try {
      // 如果 JPEG 失败，尝试作为 PNG 加载
      image = await pdfDoc.embedPng(imageBytes)
    } catch (pngError) {
      console.log(error, pngError)
      // 如果两种格式都失败，抛出错误
      throw new Error('Unsupported image format. Only JPEG and PNG are supported.')
    }
  }
  const page = pdfDoc.addPage([image.width, image.height])
  page.drawImage(image, {
    x: 0,
    y: 0,
    width: image.width,
    height: image.height,
  })
  return pdfDoc
}

export async function addWatermarkToPDF(params: {
  fileBytes: Uint8Array | string
  watermarkText: string
  isWatermark?: boolean
  isImage: boolean
  callback?: (pdfDoc?: PDFDocument) => void
}): Promise<Uint8Array> {
  const { fileBytes, watermarkText, isImage, isWatermark = true, callback } = params
  let pdfDoc: PDFDocument
  if (isImage) {
    pdfDoc = await createPDFFromImage(fileBytes)
  } else {
    pdfDoc = await PDFDocument.load(fileBytes)
  }
  callback?.(pdfDoc)

  // 注册 fontkit
  pdfDoc.registerFontkit(fontkit)

  // 加载支持中文的字体
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica)

  // 获取所有页面
  const pages = pdfDoc.getPages()

  if (isWatermark) {
    pages.forEach((page) => {
      const { width, height } = page.getSize()

      // 在每一页上绘制水印
      drawWatermark(page, watermarkText, font, {
        width,
        height,
      })
    })
  }
  // 保存修改后的 PDF
  const res = await pdfDoc.save()
  return res
}
