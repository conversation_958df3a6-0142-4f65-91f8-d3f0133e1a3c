import config from '@/config'
import { UCSDK } from '@pupu/uc-sdk'
import uc from '@galaxy/uc'
import { message } from 'antd'

const { oaConfig, loginUcAppId, environment } = config

const ucSdk = new UCSDK({
  appid: loginUcAppId,
  env: environment === 'prod' ? 'prod' : 'pre',
})

export const getOaCrossSystemOALink = (url: string, code: string): string => {
  const encodedUrl = encodeURIComponent(url)
  return `${oaConfig.url}/oauth/authorize/uc?redirect_uri=${encodedUrl}&code=${code}`
}

const getCrossSystemSilentLogin = async (appId: string): Promise<any> => {
  try {
    const res = await ucSdk.loginByCrossSystem({
      /**
       * 需要登录的应用的 uc应用id，即 B 系统的应用id
       */
      app_id: appId,
      /**
       * A系统的 token
       */
      token: uc.getToken()?.accessToken || '',
    })
    return res.code
  } catch (error: any) {
    message.error(error.message || '获取跨系统登录信息失败')
    error.apmIgnore = true
    return Promise.reject(error)
  }
}

export const skipOa = async (oaId: number) => {
  let oaUrl = ''
  try {
    const targetSystemCode = await getCrossSystemSilentLogin(oaConfig.ucAppId)
    oaUrl = getOaCrossSystemOALink(
      `/spa/workflow/static4form/index.html#/main/workflow/req?requestid=${oaId}`,
      targetSystemCode,
    )
  } catch (err) {
    console.error('登录OA异常', err)
  }
  window.open(oaUrl || config?.oaConfig?.url, '_blank')
}
