import { message } from 'antd'

export function getUrlParam(name, url) {
  return (
    decodeURIComponent(
      (new RegExp(`[?|&]${name}=([^&;]+?)(&|#|;|$)`).exec(url) || ['', ''])[1].replace(
        /\+/g,
        '%20',
      ),
    ) || null
  )
}

// 时间格式转换
export function formatDate(cTime, cFormat) {
  // 判空
  if (arguments.length === 0 || typeof cTime === 'undefined' || !cTime) {
    return null
  }
  let date = cTime
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  // 如果不是Date对象
  if (typeof date !== 'object') {
    if (typeof date === 'string') {
      if (/^[0-9]+$/.test(date)) {
        // support "1548221490638"
        date = Number(date)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        date = date.replace(/-/gm, '/')
      }
    }

    if (typeof cTime === 'number' && date.toString().length === 10) {
      date *= 1000
    }
    date = new Date(date)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }
  const timeStr = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return timeStr
}

// 毫秒转耗时（时分秒）
export const millisecondsToDate = (milliseconds) => {
  const seconds = milliseconds / 1000
  const h = Math.floor(seconds / 3600)
  const m = Math.floor((seconds / 60) % 60)
  const s = Math.floor(seconds % 60)
  return `${h < 10 ? `0${h}` : h}小时${m < 10 ? `0${m}` : m}分钟${s < 10 ? `0${s || 1}` : s}秒`
}

/**
 * 不改变源数组的splice, 返回拼接后的新数组
 */
export const spliceImmutable = (arr: any[], index: number, removeCount: number, ...elements) => [
  ...arr.slice(0, index),
  ...elements,
  ...arr.slice(index + removeCount),
]

/**
 * 根据条件获取数组里的最小元素
 */
export const findMinByKey = (arr: Record<string, any>[], key: string) =>
  arr.reduce((prev, cur) => (prev[key] - cur[key] < 0 ? prev : cur), arr[0])

/**
 * blob 转 json
 * @param blob
 * @param cb
 */
export const blobToJson = (blob: Blob, cb: (v: any) => void) => {
  const reader = new FileReader()
  reader.readAsText(blob, 'utf-8')
  reader.onload = function () {
    const data = JSON.parse(reader.result as string)
    cb(data)
  }
  reader.onerror = function () {
    cb({})
  }
}

/**
 * 解析文件名
 * @param headers
 * @returns
 */
export const analysisDisposition = (headers) => {
  const disposition = headers['content-disposition']
  let fileName = ''
  if (disposition) {
    const dispositionArr = decodeURIComponent(disposition).split('=')
    if (dispositionArr.length === 2) {
      fileName = dispositionArr[1]
    }
  }
  return fileName
}

// 是否是本地localhost或者是本地ip
export function isLocalNetwork(hostname = window.location.hostname) {
  return (
    ['localhost', '127.0.0.1', '', '::1'].includes(hostname) ||
    hostname.startsWith('192.168.') ||
    hostname.startsWith('10.0.') ||
    hostname.endsWith('.local')
  )
}

// 期数格式化
export const formatIssueNum = (issueNum, maxLen = 3) => {
  const len = `${issueNum}`.length
  if (len < maxLen) {
    return `${new Array(maxLen - len).fill('0').join('')}${issueNum}`
  }
  return issueNum
}

export const const2options = (obj) =>
  Object.keys(obj).map((key) => ({ label: obj[key], value: key }))

/**
 * 文件下载
 * @param blob 文件
 * @param fileName 文件名
 */
export const fileDownloadForBlob = (blob: Blob, fileName: string) => {
  const url = window.URL.createObjectURL(blob)
  const anchor = document.createElement('a')
  const body = document.querySelector('body')
  anchor.href = url
  anchor.download = fileName
  body?.appendChild(anchor)
  anchor.click()
  window.URL.revokeObjectURL(anchor.href)
}

/**
 * @description: 下载excel文件
 * @param {*} res
 * @return {*}
 */
export const downloadExcelFile = (res) => {
  const type = res?.data?.type
  // 后端下载失败时返回的data类型也是blob,需要解析一下获取错误信息
  if (type === 'application/json') {
    const reader = new FileReader()
    reader.readAsText(res.data, 'utf-8')
    reader.onload = function () {
      try {
        const jsonRes = typeof reader.result === 'string' && JSON.parse(reader.result) // 获取blob数据转换为json后的数据，即后台返回的原始数据
        if (jsonRes?.code !== 0) {
          message.error(jsonRes?.msg, 3)
        } else if (jsonRes?.data) {
          message.info(jsonRes.data, 3)
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err)
      }
    }
  } else {
    const blob = new Blob([res.data], {
      type: type || 'application/x-xls',
    }) // type是文件类，详情可以参阅blob文件类型
    // 创建新的URL并指向File对象或者Blob对象的地址
    const blobURL = window.URL.createObjectURL(blob)
    // 创建a标签，用于跳转至下载链接
    const tempLink = document.createElement('a')
    tempLink.style.display = 'none'
    tempLink.href = blobURL
    const fileName = decodeURIComponent(
      res.headers['content-disposition']?.split(';')[1].split('=')[1],
    )
    tempLink.setAttribute('download', fileName.replace(new RegExp('"', 'g'), ''))
    // 兼容：某些浏览器不支持HTML5的download属性
    if (typeof tempLink.download === 'undefined') {
      tempLink.setAttribute('target', '_blank')
    }
    // 挂载a标签
    document.body.appendChild(tempLink)
    tempLink.click()
    document.body.removeChild(tempLink)
    // 释放blob URL地址
    window.URL.revokeObjectURL(blobURL)
  }
}
