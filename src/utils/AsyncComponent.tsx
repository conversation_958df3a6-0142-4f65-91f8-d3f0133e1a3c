import React, { Component } from 'react'
import { Skeleton } from 'antd'

export default function asyncComponent(importComponent) {
  class AsyncComponent extends Component {
    state: {
      component: React.FC | null
    }

    isUnmounted = false

    constructor(props) {
      super(props)

      this.state = {
        component: null,
      }
    }

    async componentDidMount() {
      const { default: component } = await importComponent()

      if (!this.isUnmounted) {
        this.setComponent(component)
      }
    }

    componentWillUnmount() {
      this.isUnmounted = true
    }

    setComponent = (component) => {
      this.setState({
        component,
      })
    }

    render() {
      const C = this.state.component

      return C ? (
        <C {...this.props} />
      ) : (
        <div
          style={{
            padding: '20px',
          }}
        >
          <Skeleton />
          <Skeleton
            paragraph={{
              rows: 15,
            }}
          />
        </div>
      )
    }
  }

  return AsyncComponent
}
