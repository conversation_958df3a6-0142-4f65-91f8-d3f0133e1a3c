.loginContainer {
  width: 100%;
  height: calc(100vh);
  background: url('@/assets/login-bg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loginPageContainer {
  width: 450px;
  .copyRight {
    margin-top: 24px;
    font-size: 12px;
    line-height: 17px;
    font-weight: 500;
    line-height: 22px;
    color: #fff;
    text-align: center;
  }
}
.QrcodeBox {
  :global {
    background: #ffffff;
    box-shadow: 0px 3px 6px -4px rgba(0, 0, 0, 0.12), 0px 6px 20px rgba(0, 0, 0, 0.08),
      0px 9px 50px 8px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    padding: 40px;
    text-align: center;
    color: #999999;
    .title {
      font-weight: 500;
      color: #2264f6;
      font-size: 28px;
      line-height: 39px;
    }
    .sub-title {
      padding-top: 4px;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
    }
    .container {
      margin-top: 80px;
    }
    .tip {
      position: relative;
      font-size: 14px;
      line-height: 20px;
      &::before {
        position: absolute;
        left: 10px;
        width: 64px;
        top: 10px;
        content: '';
        height: 1px;
        background: #eeeeee;
      }
      &::after {
        position: absolute;
        right: 10px;
        width: 64px;
        top: 10px;
        content: '';
        height: 1px;
        background: #eeeeee;
      }
    }
    .sub-tip {
      font-size: 12px;
      padding-top: 4px;
      line-height: 17px;
    }
  }
}
.EntityBox {
  :global {
    position: relative;
    height: 100%;
    font-size: 14px;
    line-height: 26px;
    color: rgba(0, 0, 0, 0.45);
    background: #ffffff;
    box-shadow: 0px 3px 6px -4px rgba(0, 0, 0, 0.12), 0px 6px 20px rgba(0, 0, 0, 0.08),
      0px 9px 50px 8px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    .header {
      font-size: 16px;
      height: 56px;
      line-height: 56px;
      padding: 0 24px;
      box-shadow: inset 0px -1px 0px #e8e8e8;
      border-radius: 2px 2px 0px 0px;
      font-weight: 500;
      color: #333333;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
.SwitchEntity {
  :global {
    .@{ant-prefix}-radio-checked .@{ant-prefix}-radio-inner {
      border-color: #2264f6;
    }
    .@{ant-prefix}-radio-wrapper:hover .@{ant-prefix}-radio,
    .@{ant-prefix}-radio:hover .@{ant-prefix}-radio-inner,
    .@{ant-prefix}-radio-input:focus + .@{ant-prefix}-radio-inner {
      border-color: #2264f6;
    }
    .@{ant-prefix}-radio-inner::after {
      background-color: #2264f6;
    }
    .@{ant-prefix}-radio-checked::after {
      border: 1px solid #2264f6;
    }
    .footer {
      .@{ant-prefix}-btn:hover {
        border-color: #2264f6;
        color: #2264f6;
      }
      .@{ant-prefix}-btn-primary,
      .@{ant-prefix}-btn-primary:hover {
        background: #2264f6;
        border-color: #2264f6;
        color: #fff;
      }
      .@{ant-prefix}-btn-primary[disabled],
      .@{ant-prefix}-btn-primary[disabled]:hover,
      .@{ant-prefix}-btn-primary[disabled]:focus,
      .@{ant-prefix}-btn-primary[disabled]:active {
        background: #f5f5f5;
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }
}
.RecentlyEntity {
  :global {
    .select_container {
      .item_box {
        &:hover {
          border: 1px solid #2264f6;
        }
        &:active {
          background: #f8faff;
          border: 1px solid #96b1ec;
        }
      }
    }
    .change_text {
      color: #2264f6;
    }
  }
}
@media screen and (min-width: 1920px) {
  .loginPageContainer {
    width: 576px;
    .copyRight {
      font-size: 12px;
      line-height: 17px;
    }
  }
  .QrcodeBox {
    :global {
      padding: 56px;
      .title {
        font-size: 40px;
        line-height: 56px;
      }
      .sub-title {
        padding-top: 8px;
        font-size: 18px;
        line-height: 25px;
      }
      .container {
        margin-top: 80px;
        margin-bottom: 100px;
      }
      .tip {
        font-size: 16px;
        line-height: 22px;
        &::before {
          left: 0px;
          width: 107px;
        }
        &::after {
          right: 0px;
          width: 107px;
        }
      }
      .sub-tip {
        font-size: 14px;
        line-height: 20px;
        padding-top: 8px;
      }
    }
  }
  .EntityBox {
    :global {
      font-size: 18px;
      .header {
        font-size: 22px;
        height: 86px;
        line-height: 86px;
        padding: 0 32px;
      }
    }
  }
  .RecentlyEntity {
    :global {
      min-height: 675px;
      .select_container {
        min-height: 480px;
        font-size: 18px;
        line-height: 26px;
        .center_container {
          min-width: 441px;
          max-width: 528px;
        }
        .item_box {
          margin-bottom: 24px;
          .left {
            min-width: 246px;
            font-size: 18px;
            line-height: 26px;
          }
          .right {
            // min-width: 145px;
            font-size: 14px;
            line-height: 24px;
          }
        }
      }
      .change_text {
        font-size: 18px;
        line-height: 26px;
      }
    }
  }
  .SwitchEntity {
    :global {
      .switchEntity_body {
        min-height: 675px;
        font-size: 18px;
      }
      .@{ant-prefix}-form label {
        font-size: 18px;
      }
      .footer {
        padding: 0 32px;
        height: 82px;
        .@{ant-prefix}-btn {
          width: 80px;
          height: 40px;
          font-size: 16px;
          margin-left: 8px;
        }
      }
    }
  }
}
