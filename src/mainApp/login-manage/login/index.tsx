import React, { useState, useRef } from 'react'
import { message } from 'antd'
import uc from '@galaxy/uc'
import rbac from '@galaxy/rbac'
import { Login as UCLogin } from '@galaxy/uc-component'
import { RecentlyEntity, SwitchEntity } from '@galaxy/rbac-component'
import {
  getRecentEntity,
  setCurrentEntity,
  getUsefulEntities,
} from '@/mainApp/services/entityService'
import userService from '@/mainApp/services/userService'
// import RecentlyEntity from '@/mainApp/components/PermissionEntity/RecentlyEntity'
// import SwitchEntity from '@/mainApp/components/PermissionEntity/SwitchEntity'
import { login } from '@/mainApp/services/authService'
import style from './style.module.less'

const Login: React.FC = () => {
  const [recentVisiable, setRecentVisiable] = useState(false)
  const [switchVisiable, setSwitchVisiable] = useState(false)
  const userInfoRef: any = useRef({})

  // 切换实体权限
  const handleSwitchEntity = (params) => {
    setCurrentEntity(params)
    message.success('登录成功', 3)
    login()
  }

  const initInfo = async () => {
    const user = await uc.getUserInfo()
    userInfoRef.current = user
    userService.userInfo = user
    const entities = getUsefulEntities()
    // 无可用权限
    if (!entities.length) {
      message.success('登录成功', 3)
      login()
      return
    }
    // 单一权限直接进入
    if (entities.length === 1) {
      const entityPath: any = rbac.getEntityPath(entities[0].id)
      handleSwitchEntity({
        tenantId: entityPath[0]?.id,
        tenantName: entityPath[0]?.name,
        systemOrComponentId: entityPath[entityPath.length - 2].id,
        systemOrComponentKey: entityPath[entityPath.length - 2]?.key,
        systemOrComponentType: entityPath[entityPath.length - 2].type,
        systemOrComponentName: entityPath[entityPath.length - 2].name,
        id: entityPath[entityPath.length - 1].id,
        name: entityPath[entityPath.length - 1].name,
        systemOrComponentCode: entityPath[entityPath.length - 2]?.code,
      })
      return
    }
    const recentEntity = getRecentEntity()
    if (recentEntity.length) {
      setRecentVisiable(true)
    } else {
      setSwitchVisiable(true)
    }
  }
  const onLoginSuccess = () => {
    message.success('登录成功', 3)
    initInfo()
  }

  const onLoginError = (error) => {
    // eslint-disable-next-line no-console
    console.error(error)
  }

  const handleCancel = () => {
    const recentEntity = getRecentEntity()
    if (recentEntity.length && switchVisiable) {
      setRecentVisiable(true)
      setSwitchVisiable(false)
    } else {
      window.location.reload()
    }
  }
  const handleChangeToSwitch = () => {
    setRecentVisiable(false)
    setSwitchVisiable(true)
  }

  return (
    <div className={style.loginPageContainer}>
      {!recentVisiable && !switchVisiable && (
        <UCLogin onError={onLoginError} onSuccess={onLoginSuccess} />
      )}
      {recentVisiable && (
        <div className={style.RecentlyEntity}>
          <RecentlyEntity
            userId={userInfoRef.current.id}
            onCancel={handleChangeToSwitch}
            onConfirm={handleSwitchEntity}
          />
        </div>
      )}
      {switchVisiable && (
        <div className={style.SwitchEntity}>
          <SwitchEntity
            userId={userInfoRef.current.id}
            onCancel={handleCancel}
            onConfirm={handleSwitchEntity}
          />
        </div>
      )}
      <div className={style.copyRight}>
        推荐使用
        <a
          style={{
            color: '#fff',
          }}
          href="https://imgs.pupuapi.com/appfile/ChromeSetup.exe"
        >
          谷歌浏览器，点此下载
          <img
            style={{
              marginTop: '-3px',
            }}
            alt=""
            src={require('@/assets/Vector.png')}
          />
        </a>
      </div>
    </div>
  )
}
export default Login
