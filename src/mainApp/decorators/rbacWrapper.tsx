import React, { useEffect, useState } from 'react'
import rbac from '@galaxy/rbac'
import config from '@/config'

const { ucAppCode } = config
// uc-rbac 配置
const ucRbacConfig = {
  id: 'a510577e427f42c88219f0c912f6b0d9',
  name: '',
}

export default function rbacWrapper(WrappedComponent) {
  function Rbac(props) {
    const [loading, setLoading] = useState(true)
    useEffect(() => {
      ;(async () => {
        rbac.setEntity(ucRbacConfig)
        await rbac.getOperationResource({
          ...ucRbacConfig,
          appId: ucAppCode,
        })
        setLoading(false)
      })()
    }, [])

    if (loading) {
      return null
    }
    return <WrappedComponent {...props} />
  }
  return Rbac
}
