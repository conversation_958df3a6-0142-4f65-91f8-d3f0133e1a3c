import React, { useEffect, useState } from 'react'
import uc from '@galaxy/uc'
import userService from '@/mainApp/services/userService'
import { logout } from '@/mainApp/services/authService'

export default function authWrapper(WrappedComponent) {
  function Auth(props) {
    const [loading, setLoading] = useState(true)
    useEffect(() => {
      ;(async () => {
        const isLogin = await uc.isLogin()
        if (isLogin) {
          const userInfo = await uc.getUserInfo()
          userService.userInfo = userInfo
          setLoading(false)
          return
        }
        logout()
      })()
    }, [])

    if (loading) {
      return null
    }
    return <WrappedComponent {...props} />
  }
  return Auth
}
