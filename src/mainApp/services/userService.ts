import config from '@/config'
import { formatDate } from '@/utils/utils'

class UserService {
  private store: {
    username?: string
    userNumber?: string
    userPhone?: number
    email?: string
    userId?: number
    postName?: string
    departmentName?: string
    watermark?: string
    positionName?: string
  } = {
    username: '用户名',
  }

  set userInfo(value: any) {
    const { name, num, phone, email, id, userGroupName, departmentName, positionName } = value
    const day = formatDate(new Date(), '{y}.{m}.{d} {h}:{i}')

    this.store = {
      username: name,
      userNumber: num,
      userPhone: phone,
      email,
      userId: id,
      postName: userGroupName,
      departmentName,
      watermark: `${num}-${name}-${day}${
        config.environment !== 'prod' ? `-${config.environment.toUpperCase()}` : ''
      }`,
      positionName,
    }
  }

  get userInfo() {
    return this.store
  }
}

export default new UserService()
