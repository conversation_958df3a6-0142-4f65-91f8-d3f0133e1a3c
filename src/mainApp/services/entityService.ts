import { setCookie, getLocalStorage, setLocalStorage, jsonToUnderline } from '@galaxy/utils'
import rbac from '@galaxy/rbac'
import { omit } from 'lodash-es'
import userService from './userService'

export interface Entity {
  tenantId: number
  tenantName: string
  systemOrComponentId: number
  systemOrComponentName: string
  systemOrComponentType: number
  systemOrComponentKey: number
  systemOrComponentCode: string
  id: number
  name: string
}

const CURRENT_ENTITY = 'currentEntity'

const RECENT_ENTITY = 'recentEntity'

export const getRecentEntity = function (): Entity[] {
  let recentEntity = {}
  try {
    recentEntity = JSON.parse(getLocalStorage(RECENT_ENTITY) || '{}')
  } catch (error) {
    recentEntity = {}
  }
  const entities = recentEntity[userService.userInfo.userId] || []
  // 过滤掉最近使用的权限中无权限的部分
  return entities
    .filter((item) => {
      const entityPath = rbac.getEntityPath(item.entityId)
      if (entityPath.length !== 0 && !entityPath.some((i) => !i.isEnabled)) {
        return true
      }
      return false
    })
    .reverse()
}

export const setRecentEntity = function (entity: Entity) {
  let recentEntity = {}
  try {
    recentEntity = JSON.parse(getLocalStorage(RECENT_ENTITY) || '{}')
  } catch (error) {
    recentEntity = {}
  }
  let entities = getRecentEntity()

  entities = entities.filter((item) => item.id !== entity.id)
  entities.unshift(entity)

  if (userService.userInfo.userId) {
    recentEntity[userService.userInfo.userId] = entities.slice(0, 5)
    setLocalStorage(RECENT_ENTITY, JSON.stringify(recentEntity), 86400000)
  }
}

// 设置当前实体权限，同时需要额外的动作，封装一层
export const setCurrentEntity = function (entity: Entity) {
  rbac.setEntity(entity)

  // 简历原件爬虫需要head，存储于setCookie中
  const $entity = omit(jsonToUnderline(entity), ['entityId', 'entityName', 'systemOrComponentCode'])
  const cookieValue = {
    ...$entity,
    permission_entity_id: entity.id,
    permission_entity_name: entity.name,
    code: entity.systemOrComponentCode,
  }
  setCookie(CURRENT_ENTITY, encodeURIComponent(JSON.stringify(cookieValue)), 86400000)

  setRecentEntity(entity)
}

export const getUsefulEntities = function () {
  let entities: any[] = []
  const tenantList = rbac.getTenantList().filter((item) => item.isEnabled)
  tenantList.forEach((tenant) => {
    const systemOrComponentList = rbac
      .getSystemOrComponentList(tenant.id)
      .filter((item) => item.isEnabled)
    systemOrComponentList.forEach((systemOrComponent) => {
      entities = entities.concat(
        rbac.getEntityList(tenant.id, systemOrComponent.id).filter((item) => item.isEnabled),
      )
    })
  })
  return entities
}
