import JSZip from 'jszip'
import { saveAs } from 'file-saver'
import { message } from 'antd'
import { uploadSdk } from '@/services/upload'
import { map } from 'lodash-es'
import { useLoaderContext } from '@/components/Loader/useLoading'
import moment from 'moment'
import { processFileWatermark, cleanupBlobUrl } from '@/utils/waterMark/WaterMarkFile'

export type BatchPackDownloadParams = {
  uuid: string
  name: string
}

type ProcessedFileLink = BatchPackDownloadParams & {
  url: string
  hasWatermark: boolean
}

const useBatchPackDownload = () => {
  const { loader } = useLoaderContext()

  /**
   * 判断文件是否需要添加水印（图片或PDF）
   * @param fileName 文件名
   * @returns boolean
   */
  const needWatermark = (fileName: string): boolean => {
    const ext = fileName.toLowerCase()
    // 支持的图片格式
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    // PDF格式
    const pdfExtensions = ['.pdf']

    const supportedExtensions = [...imageExtensions, ...pdfExtensions]

    return supportedExtensions.some((extension) => ext.endsWith(extension))
  }

  const batchPackDownload = (fileArr: BatchPackDownloadParams[], fileName?: string) => {
    loader?.show('正在打包下载中...')

    Promise.all(
      map(fileArr, (item) => {
        return uploadSdk.download(item.uuid as string)
      }),
    )
      .then(async (datas) => {
        // 处理文件链接，区分是否需要水印
        const fileLinks: ProcessedFileLink[] = await Promise.all(
          map(fileArr, async (item, index): Promise<ProcessedFileLink> => {
            const originalUrl = datas[index].url

            // 判断是否需要添加水印
            if (needWatermark(item.name)) {
              try {
                const watermarkResult = await processFileWatermark({
                  fileUrl: originalUrl,
                  uuid: item.uuid,
                  fileName: item.name,
                })

                if (watermarkResult.error) {
                  console.warn(`文件水印处理失败: ${item.name}`, watermarkResult.error)
                  // 水印处理失败时使用原始文件
                  return {
                    ...item,
                    url: originalUrl,
                    hasWatermark: false,
                  }
                }

                return {
                  ...item,
                  url: watermarkResult.processedUrl,
                  hasWatermark: true,
                }
              } catch (error) {
                console.warn(`文件水印处理异常: ${item.name}`, error)
                // 处理异常时使用原始文件
                return {
                  ...item,
                  url: originalUrl,
                  hasWatermark: false,
                }
              }
            } else {
              // 不需要水印的文件直接使用原始链接
              return {
                ...item,
                url: originalUrl,
                hasWatermark: false,
              }
            }
          }),
        )

        const zip = new JSZip()

        // 使用 Promise.all 来异步获取文件内容
        await Promise.all(
          fileLinks.map(async (file) => {
            try {
              const response = await fetch(file.url)
              if (!response.ok) {
                throw new Error(`下载文件失败: ${response.status} ${response.statusText}`)
              }
              const buffer = await response.arrayBuffer()
              zip.file(file.name, new Uint8Array(buffer))
            } catch (error) {
              console.error(`文件下载失败: ${file.name}`, error)
              // 可以选择跳过这个文件或者添加一个错误提示文件
              zip.file(`错误_${file.name}.txt`, `文件下载失败: ${error}`)
            }
          }),
        )

        // 生成压缩文件
        const content = await zip.generateAsync({ type: 'blob' })

        // 使用 FileSaver.js 触发文件下载
        saveAs(content, `${fileName || `文件_${moment().valueOf()}`}.zip`)
        loader?.hide()

        // 清理生成的blob URL，释放内存
        fileLinks.forEach((file) => {
          if (file.hasWatermark && file.url) {
            cleanupBlobUrl(file.url)
          }
        })

        message.success('打包下载成功！')
      })
      .catch((error) => {
        console.error('批量下载失败:', error)
        loader?.hide()
        message.error('打包下载失败')
      })
  }

  return { batchPackDownload }
}

export default useBatchPackDownload
