const getTextWidth = (text, font = '12px') => {
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')
  context.font = font
  const metrics = context.measureText(text)
  return Math.ceil(metrics.width + 8)
}

export default (columns, dataSource, height) => {
  const columnsWithWidth = columns.map((column) =>
    Object.assign(column, {
      width:
        getTextWidth(column.title) + 16 + (column?.sorter ? 24 : 0) + (column?.tooltip ? 22 : 0),
      ellipsis: dataSource?.length !== 0 ? column?.ellipsis || true : false,
    }),
  )

  const tableWidth = columnsWithWidth.map((column) => column.width).reduce((a, b) => a + b)

  return {
    columns: columnsWithWidth,
    dataSource,
    scroll: {
      x: dataSource?.length !== 0 ? 'max-content' : tableWidth,
      y: height,
    },
  }
}
