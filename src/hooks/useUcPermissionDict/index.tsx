import rbac from '@galaxy/rbac'
import { map } from 'lodash-es'
// import { toCamelCase } from '@galaxy/utils'
import { toCamelCase } from '@/utils'
import { UC_DICT_KEY } from '@/constants'
import { useEffect, useState } from 'react'
import config from '@/config'

const { ucAppCode } = config

export type IUcAuthFieldItem = {
  code: string
  name: string
  label: string
  value: string
  [key: string]: any
}

export type IUcPermissionData = {
  [key: string]: IUcAuthFieldItem[]
}

type IUcPermissionProps = {
  authKey: string[]
}

export const useUcPermissionDict = (props: IUcPermissionProps) => {
  const { authKey } = props
  const [data, setData] = useState<IUcPermissionData>()
  const [isLoading, setIsLoading] = useState<boolean>(true)

  const loadUcData = async () => {
    const entity = rbac.getEntity()
    const datas = await Promise.all(
      map(authKey, (key) =>
        rbac
          .getResource(
            {
              id: entity?.id || '',
              appId: ucAppCode,
            },
            key,
          )
          .catch((_) => []),
      ),
    )
    const result: IUcPermissionData = {}
    map(datas, (ucData, index) => {
      const isFieldDict = [
        UC_DICT_KEY.SECURITY_AUTH_FIELD,
        UC_DICT_KEY.EMPLOYMENT_DISPUTE_AUTH_FIELD,
      ].includes(authKey[index])
      result[authKey[index]] = map(ucData || [], (item) => ({
        ...item,
        code: isFieldDict ? toCamelCase(item.code) : item.code,
        label: item.name,
        value: isFieldDict ? toCamelCase(item.code) : item.code,
      }))
    })
    setData(result)
    setIsLoading(false)
  }

  useEffect(() => {
    loadUcData()
  }, [])
  return {
    isLoading,
    data,
  }
}
