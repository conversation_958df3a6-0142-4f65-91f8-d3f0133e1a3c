// 部门UC权限过滤
export const UC_RBAC_DEPT_FILTER = { prefix: 'admin/staff_relation', code: '9900' }

// oss上传配置
export const OSS_CONFIG = {
  service: '/admin/staff_relation',
  module: 'staff_relation',
}

// 异步任务类型
export enum EXPORT_TASK_TYPE {
  SECURITY = 1,
  EMPLOY_DISPUTE = 2,
}

export const CLIENT_CODES = {
  antdPrefixCls: 'staff-relation',
}

// 外部使用页面路由
export const EXTERNAL_USE_PAGE_ROUTE = [
  '/employment-dispute-detail',
  '/employment-dispute/compensation-oa',
  '/security/compensation-oa',
  '/security/reimbursement-oa',
  '/security/no-recovery-oa',
]

/** 是否微应用 */
export const isMicroApp = (function () {
  return !!window.__POWERED_BY_QIANKUN__
})()

// UC字典配置
export const UC_DICT_CONFIG = {
  appService: 'admin/staff_relation',
}

// UC 权限项配置
export const UC_DICT_KEY = {
  // UC应用合并，修改使用主应用的权限配置编码
  SECURITY_AUTH_FIELD: '4101210003', // 安全事件字段编辑权限
  EMPLOYMENT_DISPUTE_AUTH_FIELD: '4101210004', // 用工争议字段编辑权限
  BM_HTZT: 'BM_HTZT', // 合同主体
  BM_BZGSQY: 'BM_BZGSQY', // 编制归属区域属性
  DEPT_AUTH: '4101210005', // 	部门查询权限
}

// 付款状态
export enum PAYMENT_STATUS {
  approved = 'ERS_PS_003',
  inReview = 'ERS_PS_002',
  notApproved = 'ERS_PS_004',
  notStarted = 'ERS_PS_001',
}

// table Tooltip 配置
export const TABLE_TOOLTIP_ELLIPSIS = {
  tooltipProps: {
    overlayStyle: { overflow: 'hidden' },
    overlayInnerStyle: { whiteSpace: 'pre-wrap', maxHeight: 300, overflow: 'auto' },
  },
}

// 安全事件-进度
export const SECURITY_EVENT_PROCESS = {
  // 跟进中-1
  inProgress: '1',
  // 已撤案-2
  withdrawn: '2',
  // 已结案-3
  closed: '3',
  // 结算中-4
  settlement: '4',
  // 已结算-5
  settled: '5',
}

export const SECURITY_EVENT_PROCESS_NAME = {
  [SECURITY_EVENT_PROCESS.inProgress]: '跟进中',
  [SECURITY_EVENT_PROCESS.closed]: '已结案',
  [SECURITY_EVENT_PROCESS.withdrawn]: '已撤案',
  [SECURITY_EVENT_PROCESS.settlement]: '结算中',
  [SECURITY_EVENT_PROCESS.settled]: '已结算',
}

export const SECURITY_EVENT_PROCESS_COLOR = {
  [SECURITY_EVENT_PROCESS.inProgress]: '#1890FF',
  [SECURITY_EVENT_PROCESS.closed]: '#52C41A',
  [SECURITY_EVENT_PROCESS.withdrawn]: '#9F9F9F',
  [SECURITY_EVENT_PROCESS.settlement]: '#FA8C16',
  [SECURITY_EVENT_PROCESS.settled]: '#722ED1',
}

export const YES_NO_OPTIONS = [
  {
    label: '是',
    value: '1',
  },
  {
    label: '否',
    value: '0',
  },
]

// 安全事件-未结案原因隐藏字典项
// 【仲裁/诉讼中】、【医疗期】、【医疗期(骨折)】
export const CLOSE_CASE_HIDE_VALUE = ['07', '08', '14']

// 审批状态枚举常量
export const APPROVAL_STATUS = {
  DRAFT: -1, // 草稿
  PENDING: 0, // 审批中
  APPROVED: 1, // 审批通过
  REJECTED: 2, // 已驳回
}

// 单据信息-审批状态
export const APPROVAL_STATUS_OPTIONS = [
  // OA流程状态 -1草稿，0审批中，1审批通过，2已驳回
  {
    label: '草稿',
    value: APPROVAL_STATUS.DRAFT,
  },
  {
    label: '审批中',
    value: APPROVAL_STATUS.PENDING,
  },
  {
    label: '审批通过',
    value: APPROVAL_STATUS.APPROVED,
  },
  {
    label: '已驳回',
    value: APPROVAL_STATUS.REJECTED,
  },
]

export enum INVOICE_TYPE_PAGE_MAP {
  security = 'security',
  employmentDispute = 'employment-dispute',
  compensation = 'compensation',
  reimbursement = 'reimbursement',
  noRecovery = 'no-recovery',
}

export const INVOICE_TYPE_NUMBER_MAP = {
  1: INVOICE_TYPE_PAGE_MAP.security,
  2: INVOICE_TYPE_PAGE_MAP.employmentDispute,
  3: INVOICE_TYPE_PAGE_MAP.compensation,
  4: INVOICE_TYPE_PAGE_MAP.reimbursement,
  5: INVOICE_TYPE_PAGE_MAP.noRecovery,
}

export const PAGE_MODE = {
  detail: 'detail',
  edit: 'edit',
  new: 'new',
}

export const EVENT_TYPE = {
  security: 1, // 安全事件
  securityDispute: 2, // 安全争议
  employmentDispute: 3, // 用工争议
}

export const EVENT_TYPE_OPTIONS = [
  {
    label: '安全事件',
    value: 1,
  },
  {
    label: '安全争议',
    value: 2,
  },
  {
    label: '用工争议',
    value: 3,
  },
]

export const COMPENSATION_TYPE = {
  employee: '1', // 仅员工
  thirdParty: '2', // 涉三者
}

export const ACCIDENT_NATURE = {
  employeeInjury: 'ERS_TAN_001', // 员工受伤
  thirdPartyInjury: 'ERS_TAN_002', // 三方受伤
  employeeDamage: 'ERS_TAN_003', // 员工财损
  thirdPartyDamage: 'ERS_TAN_004', // 三方财损
}

export const REPAYMENT_TYPE = {
  insurance: '1', // 保司回款
  employee: '2', // 员工回款
  recovery: '3', // 追偿回款
}

export const EVENT_CLASSIFICATION = {
  trafficAccident: 'ERS_EC_001', // A交通事故
  nonTrafficAccident: 'ERS_EC_002', // B非交通事故
}

export const FOLLOW_STATUS_NO_FOLLOW_CODE = '13'

export const RESPONSIBILITY_ALLOCATION = {
  employeeFullResponsibility: '1', // 员工全责
  employeeMainResponsibility: '2', // 员工主责
  equalResponsibility: '3', // 同等责任
  employeeNoResponsibility: '4', // 员工无责
  thirdPartyMainResponsibility: '5', // 对方主责
  bothNoResponsibility: '6', // 双方均无责
  responsibilityPending: '7', // 责任待定
  employeeSecondaryResponsibility: '8', // 员工次责
  negotiation: '9', // 协商解决
}
