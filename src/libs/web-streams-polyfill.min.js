!(function (e, r) { typeof exports === 'object' && typeof module !== 'undefined' ? r(exports) : typeof define === 'function' && define.amd ? define(['exports'], r) : r((e = typeof globalThis !== 'undefined' ? globalThis : e || self).WebStreamsPolyfill = {}) }(this, ((e) => {
  const r = typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol' ? Symbol : function (e) { return `Symbol(${e})` }; function t() {} const o = typeof self !== 'undefined' ? self : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : void 0; function n(e) { return typeof e === 'object' && e !== null || typeof e === 'function' } const a = t; const i = Promise; const l = Promise.prototype.then; const u = Promise.resolve.bind(i); const s = Promise.reject.bind(i); function c(e) { return new i(e) } function d(e) { return u(e) } function f(e) { return s(e) } function b(e, r, t) { return l.call(e, r, t) } function p(e, r, t) { b(b(e, r, t), void 0, a) } function _(e, r) { p(e, r) } function h(e, r) { p(e, void 0, r) } function m(e, r, t) { return b(e, r, t) } function y(e) { b(e, void 0, a) } const v = (function () { const e = o && o.queueMicrotask; if (typeof e === 'function') return e; const r = d(void 0); return function (e) { return b(r, e) } }()); function g(e, r, t) { if (typeof e !== 'function') throw new TypeError('Argument is not a function'); return Function.prototype.apply.call(e, r, t) } function S(e, r, t) { try { return d(g(e, r, t)) } catch (e) { return f(e) } } const w = (function () { function e() { this._cursor = 0, this._size = 0, this._front = { _elements: [], _next: void 0 }, this._back = this._front, this._cursor = 0, this._size = 0 } return Object.defineProperty(e.prototype, 'length', { get() { return this._size }, enumerable: !1, configurable: !0 }), e.prototype.push = function (e) { const r = this._back; let t = r; r._elements.length === 16383 && (t = { _elements: [], _next: void 0 }), r._elements.push(e), t !== r && (this._back = t, r._next = t), ++this._size }, e.prototype.shift = function () { const e = this._front; let r = e; const t = this._cursor; let o = t + 1; const n = e._elements; const a = n[t]; return o === 16384 && (r = e._next, o = 0), --this._size, this._cursor = o, e !== r && (this._front = r), n[t] = void 0, a }, e.prototype.forEach = function (e) { for (let r = this._cursor, t = this._front, o = t._elements; !(r === o.length && void 0 === t._next || r === o.length && (r = 0, (o = (t = t._next)._elements).length === 0));)e(o[r]), ++r }, e.prototype.peek = function () { const e = this._front; const r = this._cursor; return e._elements[r] }, e }()); function R(e, r) { e._ownerReadableStream = r, r._reader = e, r._state === 'readable' ? q(e) : r._state === 'closed' ? (function (e) { q(e), W(e) }(e)) : O(e, r._storedError) } function T(e, r) { return Tt(e._ownerReadableStream, r) } function C(e) { e._ownerReadableStream._state === 'readable' ? E(e, new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")) : (function (e, r) { O(e, r) }(e, new TypeError("Reader was released and can no longer be used to monitor the stream's closedness"))), e._ownerReadableStream._reader = void 0, e._ownerReadableStream = void 0 } function P(e) { return new TypeError(`Cannot ${e} a stream using a released reader`) } function q(e) { e._closedPromise = c(((r, t) => { e._closedPromise_resolve = r, e._closedPromise_reject = t })) } function O(e, r) { q(e), E(e, r) } function E(e, r) { void 0 !== e._closedPromise_reject && (y(e._closedPromise), e._closedPromise_reject(r), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0) } function W(e) { void 0 !== e._closedPromise_resolve && (e._closedPromise_resolve(void 0), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0) } const j = r('[[AbortSteps]]'); const B = r('[[ErrorSteps]]'); const k = r('[[CancelSteps]]'); const A = r('[[PullSteps]]'); const z = Number.isFinite || function (e) { return typeof e === 'number' && isFinite(e) }; const D = Math.trunc || function (e) { return e < 0 ? Math.ceil(e) : Math.floor(e) }; function I(e, r) { if (void 0 !== e && (typeof (t = e) !== 'object' && typeof t !== 'function')) throw new TypeError(`${r} is not an object.`); let t } function F(e, r) { if (typeof e !== 'function') throw new TypeError(`${r} is not a function.`) } function L(e, r) { if (!(function (e) { return typeof e === 'object' && e !== null || typeof e === 'function' }(e))) throw new TypeError(`${r} is not an object.`) } function M(e, r, t) { if (void 0 === e) throw new TypeError(`Parameter ${r} is required in '${t}'.`) } function Q(e, r, t) { if (void 0 === e) throw new TypeError(`${r} is required in '${t}'.`) } function Y(e) { return Number(e) } function x(e) { return e === 0 ? 0 : e } function N(e, r) { const t = Number.MAX_SAFE_INTEGER; let o = Number(e); if (o = x(o), !z(o)) throw new TypeError(`${r} is not a finite number`); if ((o = (function (e) { return x(D(e)) }(o))) < 0 || o > t) throw new TypeError(`${r} is outside the accepted range of 0 to ${t}, inclusive`); return z(o) && o !== 0 ? o : 0 } function H(e, r) { if (!wt(e)) throw new TypeError(`${r} is not a ReadableStream.`) } function V(e) { return new $(e) } function U(e, r) { e._reader._readRequests.push(r) } function G(e, r, t) { const o = e._reader._readRequests.shift(); t ? o._closeSteps() : o._chunkSteps(r) } function X(e) { return e._reader._readRequests.length } function J(e) { const r = e._reader; return void 0 !== r && !!ee(r) } let K; let Z; var $ = (function () { function ReadableStreamDefaultReader(e) { if (M(e, 1, 'ReadableStreamDefaultReader'), H(e, 'First parameter'), Rt(e)) throw new TypeError('This stream has already been locked for exclusive reading by another reader'); R(this, e), this._readRequests = new w() } return Object.defineProperty(ReadableStreamDefaultReader.prototype, 'closed', { get() { return ee(this) ? this._closedPromise : f(te('closed')) }, enumerable: !1, configurable: !0 }), ReadableStreamDefaultReader.prototype.cancel = function (e) { return void 0 === e && (e = void 0), ee(this) ? void 0 === this._ownerReadableStream ? f(P('cancel')) : T(this, e) : f(te('cancel')) }, ReadableStreamDefaultReader.prototype.read = function () { if (!ee(this)) return f(te('read')); if (void 0 === this._ownerReadableStream) return f(P('read from')); let e; let r; const t = c(((t, o) => { e = t, r = o })); return re(this, { _chunkSteps(r) { return e({ value: r, done: !1 }) }, _closeSteps() { return e({ value: void 0, done: !0 }) }, _errorSteps(e) { return r(e) } }), t }, ReadableStreamDefaultReader.prototype.releaseLock = function () { if (!ee(this)) throw te('releaseLock'); if (void 0 !== this._ownerReadableStream) { if (this._readRequests.length > 0) throw new TypeError('Tried to release a reader lock when that reader has pending read() calls un-settled'); C(this) } }, ReadableStreamDefaultReader }()); function ee(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_readRequests') && e instanceof $) } function re(e, r) { const t = e._ownerReadableStream; t._disturbed = !0, t._state === 'closed' ? r._closeSteps() : t._state === 'errored' ? r._errorSteps(t._storedError) : t._readableStreamController[A](r) } function te(e) { return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`) }Object.defineProperties($.prototype, { cancel: { enumerable: !0 }, read: { enumerable: !0 }, releaseLock: { enumerable: !0 }, closed: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty($.prototype, r.toStringTag, { value: 'ReadableStreamDefaultReader', configurable: !0 }), typeof r.asyncIterator === 'symbol' && ((K = {})[r.asyncIterator] = function () { return this }, Z = K, Object.defineProperty(Z, r.asyncIterator, { enumerable: !1 })); const oe = (function () { function e(e, r) { this._ongoingPromise = void 0, this._isFinished = !1, this._reader = e, this._preventCancel = r } return e.prototype.next = function () { const e = this; const r = function () { return e._nextSteps() }; return this._ongoingPromise = this._ongoingPromise ? m(this._ongoingPromise, r, r) : r(), this._ongoingPromise }, e.prototype.return = function (e) { const r = this; const t = function () { return r._returnSteps(e) }; return this._ongoingPromise ? m(this._ongoingPromise, t, t) : t() }, e.prototype._nextSteps = function () { const e = this; if (this._isFinished) return Promise.resolve({ value: void 0, done: !0 }); let r; let t; const o = this._reader; if (void 0 === o._ownerReadableStream) return f(P('iterate')); const n = c(((e, o) => { r = e, t = o })); return re(o, { _chunkSteps(t) { e._ongoingPromise = void 0, v((() => { return r({ value: t, done: !1 }) })) }, _closeSteps() { e._ongoingPromise = void 0, e._isFinished = !0, C(o), r({ value: void 0, done: !0 }) }, _errorSteps(r) { e._ongoingPromise = void 0, e._isFinished = !0, C(o), t(r) } }), n }, e.prototype._returnSteps = function (e) { if (this._isFinished) return Promise.resolve({ value: e, done: !0 }); this._isFinished = !0; const r = this._reader; if (void 0 === r._ownerReadableStream) return f(P('finish iterating')); if (!this._preventCancel) { const t = T(r, e); return C(r), m(t, (() => { return { value: e, done: !0 } })) } return C(r), d({ value: e, done: !0 }) }, e }()); const ne = { next() { return ae(this) ? this._asyncIteratorImpl.next() : f(ie('next')) }, return(e) { return ae(this) ? this._asyncIteratorImpl.return(e) : f(ie('return')) } }; function ae(e) { if (!n(e)) return !1; if (!Object.prototype.hasOwnProperty.call(e, '_asyncIteratorImpl')) return !1; try { return e._asyncIteratorImpl instanceof oe } catch (e) { return !1 } } function ie(e) { return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`) } void 0 !== Z && Object.setPrototypeOf(ne, Z); const le = Number.isNaN || function (e) { return e != e }; function ue(e) { return e.slice() } function se(e, r, t, o, n) { new Uint8Array(e).set(new Uint8Array(t, o, n), r) } function ce(e, r, t) { if (e.slice) return e.slice(r, t); const o = t - r; const n = new ArrayBuffer(o); return se(n, 0, e, r, o), n } function de(e) { const r = ce(e.buffer, e.byteOffset, e.byteOffset + e.byteLength); return new Uint8Array(r) } function fe(e) { const r = e._queue.shift(); return e._queueTotalSize -= r.size, e._queueTotalSize < 0 && (e._queueTotalSize = 0), r.value } function be(e, r, t) { if (typeof (o = t) !== 'number' || le(o) || o < 0 || t === 1 / 0) throw new RangeError('Size must be a finite, non-NaN, non-negative number.'); let o; e._queue.push({ value: r, size: t }), e._queueTotalSize += t } function pe(e) { e._queue = new w(), e._queueTotalSize = 0 } const _e = (function () { function ReadableStreamBYOBRequest() { throw new TypeError('Illegal constructor') } return Object.defineProperty(ReadableStreamBYOBRequest.prototype, 'view', { get() { if (!ye(this)) throw Me('view'); return this._view }, enumerable: !1, configurable: !0 }), ReadableStreamBYOBRequest.prototype.respond = function (e) { if (!ye(this)) throw Me('respond'); if (M(e, 1, 'respond'), e = N(e, 'First parameter'), void 0 === this._associatedReadableByteStreamController) throw new TypeError('This BYOB request has been invalidated'); this._view.buffer, Ie(this._associatedReadableByteStreamController, e) }, ReadableStreamBYOBRequest.prototype.respondWithNewView = function (e) { if (!ye(this)) throw Me('respondWithNewView'); if (M(e, 1, 'respondWithNewView'), !ArrayBuffer.isView(e)) throw new TypeError('You can only respond with array buffer views'); if (void 0 === this._associatedReadableByteStreamController) throw new TypeError('This BYOB request has been invalidated'); e.buffer, Fe(this._associatedReadableByteStreamController, e) }, ReadableStreamBYOBRequest }()); Object.defineProperties(_e.prototype, { respond: { enumerable: !0 }, respondWithNewView: { enumerable: !0 }, view: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(_e.prototype, r.toStringTag, { value: 'ReadableStreamBYOBRequest', configurable: !0 }); const he = (function () { function ReadableByteStreamController() { throw new TypeError('Illegal constructor') } return Object.defineProperty(ReadableByteStreamController.prototype, 'byobRequest', { get() { if (!me(this)) throw Qe('byobRequest'); return ze(this) }, enumerable: !1, configurable: !0 }), Object.defineProperty(ReadableByteStreamController.prototype, 'desiredSize', { get() { if (!me(this)) throw Qe('desiredSize'); return De(this) }, enumerable: !1, configurable: !0 }), ReadableByteStreamController.prototype.close = function () { if (!me(this)) throw Qe('close'); if (this._closeRequested) throw new TypeError('The stream has already been closed; do not close it again!'); const e = this._controlledReadableByteStream._state; if (e !== 'readable') throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`); Be(this) }, ReadableByteStreamController.prototype.enqueue = function (e) { if (!me(this)) throw Qe('enqueue'); if (M(e, 1, 'enqueue'), !ArrayBuffer.isView(e)) throw new TypeError('chunk must be an array buffer view'); if (e.byteLength === 0) throw new TypeError('chunk must have non-zero byteLength'); if (e.buffer.byteLength === 0) throw new TypeError("chunk's buffer must have non-zero byteLength"); if (this._closeRequested) throw new TypeError('stream is closed or draining'); const r = this._controlledReadableByteStream._state; if (r !== 'readable') throw new TypeError(`The stream (in ${r} state) is not in the readable state and cannot be enqueued to`); ke(this, e) }, ReadableByteStreamController.prototype.error = function (e) { if (void 0 === e && (e = void 0), !me(this)) throw Qe('error'); Ae(this, e) }, ReadableByteStreamController.prototype[k] = function (e) { ge(this), pe(this); const r = this._cancelAlgorithm(e); return je(this), r }, ReadableByteStreamController.prototype[A] = function (e) { const r = this._controlledReadableByteStream; if (this._queueTotalSize > 0) { const t = this._queue.shift(); this._queueTotalSize -= t.byteLength, Pe(this); const o = new Uint8Array(t.buffer, t.byteOffset, t.byteLength); e._chunkSteps(o) } else { const n = this._autoAllocateChunkSize; if (void 0 !== n) { let a = void 0; try { a = new ArrayBuffer(n) } catch (r) { return void e._errorSteps(r) } const i = { buffer: a, bufferByteLength: n, byteOffset: 0, byteLength: n, bytesFilled: 0, elementSize: 1, viewConstructor: Uint8Array, readerType: 'default' }; this._pendingPullIntos.push(i) }U(r, e), ve(this) } }, ReadableByteStreamController }()); function me(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_controlledReadableByteStream') && e instanceof he) } function ye(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_associatedReadableByteStreamController') && e instanceof _e) } function ve(e) { (function (e) { const r = e._controlledReadableByteStream; if (r._state !== 'readable') return !1; if (e._closeRequested) return !1; if (!e._started) return !1; if (J(r) && X(r) > 0) return !0; if (He(r) && Ne(r) > 0) return !0; if (De(e) > 0) return !0; return !1 }(e)) && (e._pulling ? e._pullAgain = !0 : (e._pulling = !0, p(e._pullAlgorithm(), (() => { e._pulling = !1, e._pullAgain && (e._pullAgain = !1, ve(e)) }), ((r) => { Ae(e, r) })))) } function ge(e) { qe(e), e._pendingPullIntos = new w() } function Se(e, r) { let t = !1; e._state === 'closed' && (t = !0); const o = we(r); r.readerType === 'default' ? G(e, o, t) : (function (e, r, t) { const o = e._reader._readIntoRequests.shift(); t ? o._closeSteps(r) : o._chunkSteps(r) }(e, o, t)) } function we(e) { const r = e.bytesFilled; const t = e.elementSize; return new e.viewConstructor(e.buffer, e.byteOffset, r / t) } function Re(e, r, t, o) { e._queue.push({ buffer: r, byteOffset: t, byteLength: o }), e._queueTotalSize += o } function Te(e, r) { const t = r.elementSize; const o = r.bytesFilled - r.bytesFilled % t; const n = Math.min(e._queueTotalSize, r.byteLength - r.bytesFilled); const a = r.bytesFilled + n; const i = a - a % t; let l = n; let u = !1; i > o && (l = i - r.bytesFilled, u = !0); for (let s = e._queue; l > 0;) { const c = s.peek(); const d = Math.min(l, c.byteLength); const f = r.byteOffset + r.bytesFilled; se(r.buffer, f, c.buffer, c.byteOffset, d), c.byteLength === d ? s.shift() : (c.byteOffset += d, c.byteLength -= d), e._queueTotalSize -= d, Ce(e, d, r), l -= d } return u } function Ce(e, r, t) { t.bytesFilled += r } function Pe(e) { e._queueTotalSize === 0 && e._closeRequested ? (je(e), Ct(e._controlledReadableByteStream)) : ve(e) } function qe(e) { e._byobRequest !== null && (e._byobRequest._associatedReadableByteStreamController = void 0, e._byobRequest._view = null, e._byobRequest = null) } function Oe(e) { for (;e._pendingPullIntos.length > 0;) { if (e._queueTotalSize === 0) return; const r = e._pendingPullIntos.peek(); Te(e, r) && (We(e), Se(e._controlledReadableByteStream, r)) } } function Ee(e, r) { const t = e._pendingPullIntos.peek(); qe(e), e._controlledReadableByteStream._state === 'closed' ? (function (e, r) { const t = e._controlledReadableByteStream; if (He(t)) for (;Ne(t) > 0;)Se(t, We(e)) }(e)) : (function (e, r, t) { if (Ce(0, r, t), !(t.bytesFilled < t.elementSize)) { We(e); const o = t.bytesFilled % t.elementSize; if (o > 0) { const n = t.byteOffset + t.bytesFilled; const a = ce(t.buffer, n - o, n); Re(e, a, 0, a.byteLength) }t.bytesFilled -= o, Se(e._controlledReadableByteStream, t), Oe(e) } }(e, r, t)), ve(e) } function We(e) { return e._pendingPullIntos.shift() } function je(e) { e._pullAlgorithm = void 0, e._cancelAlgorithm = void 0 } function Be(e) { const r = e._controlledReadableByteStream; if (!e._closeRequested && r._state === 'readable') if (e._queueTotalSize > 0)e._closeRequested = !0; else { if (e._pendingPullIntos.length > 0) if (e._pendingPullIntos.peek().bytesFilled > 0) { const t = new TypeError('Insufficient bytes to fill elements in the given buffer'); throw Ae(e, t), t }je(e), Ct(r) } } function ke(e, r) { const t = e._controlledReadableByteStream; if (!e._closeRequested && t._state === 'readable') { const o = r.buffer; const n = r.byteOffset; const a = r.byteLength; const i = o; if (e._pendingPullIntos.length > 0) { const l = e._pendingPullIntos.peek(); l.buffer, 0, l.buffer = l.buffer } if (qe(e), J(t)) if (X(t) === 0)Re(e, i, n, a); else e._pendingPullIntos.length > 0 && We(e), G(t, new Uint8Array(i, n, a), !1); else He(t) ? (Re(e, i, n, a), Oe(e)) : Re(e, i, n, a); ve(e) } } function Ae(e, r) { const t = e._controlledReadableByteStream; t._state === 'readable' && (ge(e), pe(e), je(e), Pt(t, r)) } function ze(e) { if (e._byobRequest === null && e._pendingPullIntos.length > 0) { const r = e._pendingPullIntos.peek(); const t = new Uint8Array(r.buffer, r.byteOffset + r.bytesFilled, r.byteLength - r.bytesFilled); const o = Object.create(_e.prototype); !(function (e, r, t) { e._associatedReadableByteStreamController = r, e._view = t }(o, e, t)), e._byobRequest = o } return e._byobRequest } function De(e) { const r = e._controlledReadableByteStream._state; return r === 'errored' ? null : r === 'closed' ? 0 : e._strategyHWM - e._queueTotalSize } function Ie(e, r) { const t = e._pendingPullIntos.peek(); if (e._controlledReadableByteStream._state === 'closed') { if (r !== 0) throw new TypeError('bytesWritten must be 0 when calling respond() on a closed stream') } else { if (r === 0) throw new TypeError('bytesWritten must be greater than 0 when calling respond() on a readable stream'); if (t.bytesFilled + r > t.byteLength) throw new RangeError('bytesWritten out of range') }t.buffer = t.buffer, Ee(e, r) } function Fe(e, r) { const t = e._pendingPullIntos.peek(); if (e._controlledReadableByteStream._state === 'closed') { if (r.byteLength !== 0) throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream") } else if (r.byteLength === 0) throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream"); if (t.byteOffset + t.bytesFilled !== r.byteOffset) throw new RangeError('The region specified by view does not match byobRequest'); if (t.bufferByteLength !== r.buffer.byteLength) throw new RangeError('The buffer of view has different capacity than byobRequest'); if (t.bytesFilled + r.byteLength > t.byteLength) throw new RangeError('The region specified by view is larger than byobRequest'); const o = r.byteLength; t.buffer = r.buffer, Ee(e, o) } function Le(e, r, t, o, n, a, i) { r._controlledReadableByteStream = e, r._pullAgain = !1, r._pulling = !1, r._byobRequest = null, r._queue = r._queueTotalSize = void 0, pe(r), r._closeRequested = !1, r._started = !1, r._strategyHWM = a, r._pullAlgorithm = o, r._cancelAlgorithm = n, r._autoAllocateChunkSize = i, r._pendingPullIntos = new w(), e._readableStreamController = r, p(d(t()), (() => { r._started = !0, ve(r) }), ((e) => { Ae(r, e) })) } function Me(e) { return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`) } function Qe(e) { return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`) } function Ye(e) { return new Ve(e) } function xe(e, r) { e._reader._readIntoRequests.push(r) } function Ne(e) { return e._reader._readIntoRequests.length } function He(e) { const r = e._reader; return void 0 !== r && !!Ue(r) }Object.defineProperties(he.prototype, { close: { enumerable: !0 }, enqueue: { enumerable: !0 }, error: { enumerable: !0 }, byobRequest: { enumerable: !0 }, desiredSize: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(he.prototype, r.toStringTag, { value: 'ReadableByteStreamController', configurable: !0 }); var Ve = (function () { function ReadableStreamBYOBReader(e) { if (M(e, 1, 'ReadableStreamBYOBReader'), H(e, 'First parameter'), Rt(e)) throw new TypeError('This stream has already been locked for exclusive reading by another reader'); if (!me(e._readableStreamController)) throw new TypeError('Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source'); R(this, e), this._readIntoRequests = new w() } return Object.defineProperty(ReadableStreamBYOBReader.prototype, 'closed', { get() { return Ue(this) ? this._closedPromise : f(Xe('closed')) }, enumerable: !1, configurable: !0 }), ReadableStreamBYOBReader.prototype.cancel = function (e) { return void 0 === e && (e = void 0), Ue(this) ? void 0 === this._ownerReadableStream ? f(P('cancel')) : T(this, e) : f(Xe('cancel')) }, ReadableStreamBYOBReader.prototype.read = function (e) { if (!Ue(this)) return f(Xe('read')); if (!ArrayBuffer.isView(e)) return f(new TypeError('view must be an array buffer view')); if (e.byteLength === 0) return f(new TypeError('view must have non-zero byteLength')); if (e.buffer.byteLength === 0) return f(new TypeError("view's buffer must have non-zero byteLength")); if (e.buffer, void 0 === this._ownerReadableStream) return f(P('read from')); let r; let t; const o = c(((e, o) => { r = e, t = o })); return Ge(this, e, { _chunkSteps(e) { return r({ value: e, done: !1 }) }, _closeSteps(e) { return r({ value: e, done: !0 }) }, _errorSteps(e) { return t(e) } }), o }, ReadableStreamBYOBReader.prototype.releaseLock = function () { if (!Ue(this)) throw Xe('releaseLock'); if (void 0 !== this._ownerReadableStream) { if (this._readIntoRequests.length > 0) throw new TypeError('Tried to release a reader lock when that reader has pending read() calls un-settled'); C(this) } }, ReadableStreamBYOBReader }()); function Ue(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_readIntoRequests') && e instanceof Ve) } function Ge(e, r, t) { const o = e._ownerReadableStream; o._disturbed = !0, o._state === 'errored' ? t._errorSteps(o._storedError) : (function (e, r, t) { const o = e._controlledReadableByteStream; let n = 1; r.constructor !== DataView && (n = r.constructor.BYTES_PER_ELEMENT); const a = r.constructor; const i = r.buffer; const l = { buffer: i, bufferByteLength: i.byteLength, byteOffset: r.byteOffset, byteLength: r.byteLength, bytesFilled: 0, elementSize: n, viewConstructor: a, readerType: 'byob' }; if (e._pendingPullIntos.length > 0) return e._pendingPullIntos.push(l), void xe(o, t); if (o._state !== 'closed') { if (e._queueTotalSize > 0) { if (Te(e, l)) { const u = we(l); return Pe(e), void t._chunkSteps(u) } if (e._closeRequested) { const s = new TypeError('Insufficient bytes to fill elements in the given buffer'); return Ae(e, s), void t._errorSteps(s) } }e._pendingPullIntos.push(l), xe(o, t), ve(e) } else { const c = new a(l.buffer, l.byteOffset, 0); t._closeSteps(c) } }(o._readableStreamController, r, t)) } function Xe(e) { return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`) } function Je(e, r) { const t = e.highWaterMark; if (void 0 === t) return r; if (le(t) || t < 0) throw new RangeError('Invalid highWaterMark'); return t } function Ke(e) { const r = e.size; return r || function () { return 1 } } function Ze(e, r) { I(e, r); const t = e == null ? void 0 : e.highWaterMark; const o = e == null ? void 0 : e.size; return { highWaterMark: void 0 === t ? void 0 : Y(t), size: void 0 === o ? void 0 : $e(o, `${r} has member 'size' that`) } } function $e(e, r) { return F(e, r), function (r) { return Y(e(r)) } } function er(e, r, t) { return F(e, t), function (t) { return S(e, r, [t]) } } function rr(e, r, t) { return F(e, t), function () { return S(e, r, []) } } function tr(e, r, t) { return F(e, t), function (t) { return g(e, r, [t]) } } function or(e, r, t) { return F(e, t), function (t, o) { return S(e, r, [t, o]) } } function nr(e, r) { if (!sr(e)) throw new TypeError(`${r} is not a WritableStream.`) }Object.defineProperties(Ve.prototype, { cancel: { enumerable: !0 }, read: { enumerable: !0 }, releaseLock: { enumerable: !0 }, closed: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(Ve.prototype, r.toStringTag, { value: 'ReadableStreamBYOBReader', configurable: !0 }); const ar = typeof AbortController === 'function'; const ir = (function () { function WritableStream(e, r) { void 0 === e && (e = {}), void 0 === r && (r = {}), void 0 === e ? e = null : L(e, 'First parameter'); const t = Ze(r, 'Second parameter'); const o = (function (e, r) { I(e, r); const t = e == null ? void 0 : e.abort; const o = e == null ? void 0 : e.close; const n = e == null ? void 0 : e.start; const a = e == null ? void 0 : e.type; const i = e == null ? void 0 : e.write; return { abort: void 0 === t ? void 0 : er(t, e, `${r} has member 'abort' that`), close: void 0 === o ? void 0 : rr(o, e, `${r} has member 'close' that`), start: void 0 === n ? void 0 : tr(n, e, `${r} has member 'start' that`), write: void 0 === i ? void 0 : or(i, e, `${r} has member 'write' that`), type: a } }(e, 'First parameter')); if (ur(this), void 0 !== o.type) throw new RangeError('Invalid type is specified'); const n = Ke(t); !(function (e, r, t, o) { const n = Object.create(qr.prototype); let a = function () {}; let i = function () { return d(void 0) }; let l = function () { return d(void 0) }; let u = function () { return d(void 0) }; void 0 !== r.start && (a = function () { return r.start(n) }); void 0 !== r.write && (i = function (e) { return r.write(e, n) }); void 0 !== r.close && (l = function () { return r.close() }); void 0 !== r.abort && (u = function (e) { return r.abort(e) }); Er(e, n, a, i, l, u, t, o) }(this, o, Je(t, 1), n)) } return Object.defineProperty(WritableStream.prototype, 'locked', { get() { if (!sr(this)) throw Dr('locked'); return cr(this) }, enumerable: !1, configurable: !0 }), WritableStream.prototype.abort = function (e) { return void 0 === e && (e = void 0), sr(this) ? cr(this) ? f(new TypeError('Cannot abort a stream that already has a writer')) : dr(this, e) : f(Dr('abort')) }, WritableStream.prototype.close = function () { return sr(this) ? cr(this) ? f(new TypeError('Cannot close a stream that already has a writer')) : hr(this) ? f(new TypeError('Cannot close an already-closing stream')) : fr(this) : f(Dr('close')) }, WritableStream.prototype.getWriter = function () { if (!sr(this)) throw Dr('getWriter'); return lr(this) }, WritableStream }()); function lr(e) { return new vr(e) } function ur(e) { e._state = 'writable', e._storedError = void 0, e._writer = void 0, e._writableStreamController = void 0, e._writeRequests = new w(), e._inFlightWriteRequest = void 0, e._closeRequest = void 0, e._inFlightCloseRequest = void 0, e._pendingAbortRequest = void 0, e._backpressure = !1 } function sr(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_writableStreamController') && e instanceof ir) } function cr(e) { return void 0 !== e._writer } function dr(e, r) { let t; if (e._state === 'closed' || e._state === 'errored') return d(void 0); e._writableStreamController._abortReason = r, (t = e._writableStreamController._abortController) === null || void 0 === t || t.abort(); const o = e._state; if (o === 'closed' || o === 'errored') return d(void 0); if (void 0 !== e._pendingAbortRequest) return e._pendingAbortRequest._promise; let n = !1; o === 'erroring' && (n = !0, r = void 0); const a = c(((t, o) => { e._pendingAbortRequest = { _promise: void 0, _resolve: t, _reject: o, _reason: r, _wasAlreadyErroring: n } })); return e._pendingAbortRequest._promise = a, n || pr(e, r), a } function fr(e) { const r = e._state; if (r === 'closed' || r === 'errored') return f(new TypeError(`The stream (in ${r} state) is not in the writable state and cannot be closed`)); let t; const o = c(((r, t) => { const o = { _resolve: r, _reject: t }; e._closeRequest = o })); const n = e._writer; return void 0 !== n && e._backpressure && r === 'writable' && Gr(n), be(t = e._writableStreamController, Pr, 0), Br(t), o } function br(e, r) { e._state !== 'writable' ? _r(e) : pr(e, r) } function pr(e, r) { const t = e._writableStreamController; e._state = 'erroring', e._storedError = r; const o = e._writer; void 0 !== o && Rr(o, r), !(function (e) { if (void 0 === e._inFlightWriteRequest && void 0 === e._inFlightCloseRequest) return !1; return !0 }(e)) && t._started && _r(e) } function _r(e) { e._state = 'errored', e._writableStreamController[B](); const r = e._storedError; if (e._writeRequests.forEach(((e) => { e._reject(r) })), e._writeRequests = new w(), void 0 !== e._pendingAbortRequest) { const t = e._pendingAbortRequest; if (e._pendingAbortRequest = void 0, t._wasAlreadyErroring) return t._reject(r), void mr(e); p(e._writableStreamController[j](t._reason), (() => { t._resolve(), mr(e) }), ((r) => { t._reject(r), mr(e) })) } else mr(e) } function hr(e) { return void 0 !== e._closeRequest || void 0 !== e._inFlightCloseRequest } function mr(e) { void 0 !== e._closeRequest && (e._closeRequest._reject(e._storedError), e._closeRequest = void 0); const r = e._writer; void 0 !== r && Yr(r, e._storedError) } function yr(e, r) { const t = e._writer; void 0 !== t && r !== e._backpressure && (r ? (function (e) { Nr(e) }(t)) : Gr(t)), e._backpressure = r }Object.defineProperties(ir.prototype, { abort: { enumerable: !0 }, close: { enumerable: !0 }, getWriter: { enumerable: !0 }, locked: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(ir.prototype, r.toStringTag, { value: 'WritableStream', configurable: !0 }); var vr = (function () { function WritableStreamDefaultWriter(e) { if (M(e, 1, 'WritableStreamDefaultWriter'), nr(e, 'First parameter'), cr(e)) throw new TypeError('This stream has already been locked for exclusive writing by another writer'); this._ownerWritableStream = e, e._writer = this; let r; const t = e._state; if (t === 'writable')!hr(e) && e._backpressure ? Nr(this) : Vr(this), Mr(this); else if (t === 'erroring')Hr(this, e._storedError), Mr(this); else if (t === 'closed')Vr(this), Mr(r = this), xr(r); else { const o = e._storedError; Hr(this, o), Qr(this, o) } } return Object.defineProperty(WritableStreamDefaultWriter.prototype, 'closed', { get() { return gr(this) ? this._closedPromise : f(Fr('closed')) }, enumerable: !1, configurable: !0 }), Object.defineProperty(WritableStreamDefaultWriter.prototype, 'desiredSize', { get() { if (!gr(this)) throw Fr('desiredSize'); if (void 0 === this._ownerWritableStream) throw Lr('desiredSize'); return (function (e) { const r = e._ownerWritableStream; const t = r._state; if (t === 'errored' || t === 'erroring') return null; if (t === 'closed') return 0; return jr(r._writableStreamController) }(this)) }, enumerable: !1, configurable: !0 }), Object.defineProperty(WritableStreamDefaultWriter.prototype, 'ready', { get() { return gr(this) ? this._readyPromise : f(Fr('ready')) }, enumerable: !1, configurable: !0 }), WritableStreamDefaultWriter.prototype.abort = function (e) { return void 0 === e && (e = void 0), gr(this) ? void 0 === this._ownerWritableStream ? f(Lr('abort')) : (function (e, r) { return dr(e._ownerWritableStream, r) }(this, e)) : f(Fr('abort')) }, WritableStreamDefaultWriter.prototype.close = function () { if (!gr(this)) return f(Fr('close')); const e = this._ownerWritableStream; return void 0 === e ? f(Lr('close')) : hr(e) ? f(new TypeError('Cannot close an already-closing stream')) : Sr(this) }, WritableStreamDefaultWriter.prototype.releaseLock = function () { if (!gr(this)) throw Fr('releaseLock'); void 0 !== this._ownerWritableStream && Tr(this) }, WritableStreamDefaultWriter.prototype.write = function (e) { return void 0 === e && (e = void 0), gr(this) ? void 0 === this._ownerWritableStream ? f(Lr('write to')) : Cr(this, e) : f(Fr('write')) }, WritableStreamDefaultWriter }()); function gr(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_ownerWritableStream') && e instanceof vr) } function Sr(e) { return fr(e._ownerWritableStream) } function wr(e, r) { e._closedPromiseState === 'pending' ? Yr(e, r) : (function (e, r) { Qr(e, r) }(e, r)) } function Rr(e, r) { e._readyPromiseState === 'pending' ? Ur(e, r) : (function (e, r) { Hr(e, r) }(e, r)) } function Tr(e) { const r = e._ownerWritableStream; const t = new TypeError("Writer was released and can no longer be used to monitor the stream's closedness"); Rr(e, t), wr(e, t), r._writer = void 0, e._ownerWritableStream = void 0 } function Cr(e, r) { const t = e._ownerWritableStream; const o = t._writableStreamController; const n = (function (e, r) { try { return e._strategySizeAlgorithm(r) } catch (r) { return kr(e, r), 1 } }(o, r)); if (t !== e._ownerWritableStream) return f(Lr('write to')); const a = t._state; if (a === 'errored') return f(t._storedError); if (hr(t) || a === 'closed') return f(new TypeError('The stream is closing or closed and cannot be written to')); if (a === 'erroring') return f(t._storedError); const i = (function (e) { return c(((r, t) => { const o = { _resolve: r, _reject: t }; e._writeRequests.push(o) })) }(t)); return (function (e, r, t) { try { be(e, r, t) } catch (r) { return void kr(e, r) } const o = e._controlledWritableStream; if (!hr(o) && o._state === 'writable') { yr(o, Ar(e)) }Br(e) }(o, r, n)), i }Object.defineProperties(vr.prototype, { abort: { enumerable: !0 }, close: { enumerable: !0 }, releaseLock: { enumerable: !0 }, write: { enumerable: !0 }, closed: { enumerable: !0 }, desiredSize: { enumerable: !0 }, ready: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(vr.prototype, r.toStringTag, { value: 'WritableStreamDefaultWriter', configurable: !0 }); var Pr = {}; var qr = (function () { function WritableStreamDefaultController() { throw new TypeError('Illegal constructor') } return Object.defineProperty(WritableStreamDefaultController.prototype, 'abortReason', { get() { if (!Or(this)) throw Ir('abortReason'); return this._abortReason }, enumerable: !1, configurable: !0 }), Object.defineProperty(WritableStreamDefaultController.prototype, 'signal', { get() { if (!Or(this)) throw Ir('signal'); if (void 0 === this._abortController) throw new TypeError('WritableStreamDefaultController.prototype.signal is not supported'); return this._abortController.signal }, enumerable: !1, configurable: !0 }), WritableStreamDefaultController.prototype.error = function (e) { if (void 0 === e && (e = void 0), !Or(this)) throw Ir('error'); this._controlledWritableStream._state === 'writable' && zr(this, e) }, WritableStreamDefaultController.prototype[j] = function (e) { const r = this._abortAlgorithm(e); return Wr(this), r }, WritableStreamDefaultController.prototype[B] = function () { pe(this) }, WritableStreamDefaultController }()); function Or(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_controlledWritableStream') && e instanceof qr) } function Er(e, r, t, o, n, a, i, l) { r._controlledWritableStream = e, e._writableStreamController = r, r._queue = void 0, r._queueTotalSize = void 0, pe(r), r._abortReason = void 0, r._abortController = (function () { if (ar) return new AbortController() }()), r._started = !1, r._strategySizeAlgorithm = l, r._strategyHWM = i, r._writeAlgorithm = o, r._closeAlgorithm = n, r._abortAlgorithm = a; const u = Ar(r); yr(e, u), p(d(t()), (() => { r._started = !0, Br(r) }), ((t) => { r._started = !0, br(e, t) })) } function Wr(e) { e._writeAlgorithm = void 0, e._closeAlgorithm = void 0, e._abortAlgorithm = void 0, e._strategySizeAlgorithm = void 0 } function jr(e) { return e._strategyHWM - e._queueTotalSize } function Br(e) { const r = e._controlledWritableStream; if (e._started && void 0 === r._inFlightWriteRequest) if (r._state !== 'erroring') { if (e._queue.length !== 0) { const t = e._queue.peek().value; t === Pr ? (function (e) { const r = e._controlledWritableStream; (function (e) { e._inFlightCloseRequest = e._closeRequest, e._closeRequest = void 0 }(r)), fe(e); const t = e._closeAlgorithm(); Wr(e), p(t, (() => { !(function (e) { e._inFlightCloseRequest._resolve(void 0), e._inFlightCloseRequest = void 0, e._state === 'erroring' && (e._storedError = void 0, void 0 !== e._pendingAbortRequest && (e._pendingAbortRequest._resolve(), e._pendingAbortRequest = void 0)), e._state = 'closed'; const r = e._writer; void 0 !== r && xr(r) }(r)) }), ((e) => { !(function (e, r) { e._inFlightCloseRequest._reject(r), e._inFlightCloseRequest = void 0, void 0 !== e._pendingAbortRequest && (e._pendingAbortRequest._reject(r), e._pendingAbortRequest = void 0), br(e, r) }(r, e)) })) }(e)) : (function (e, r) { const t = e._controlledWritableStream; (function (e) { e._inFlightWriteRequest = e._writeRequests.shift() }(t)), p(e._writeAlgorithm(r), (() => { !(function (e) { e._inFlightWriteRequest._resolve(void 0), e._inFlightWriteRequest = void 0 }(t)); const r = t._state; if (fe(e), !hr(t) && r === 'writable') { const o = Ar(e); yr(t, o) }Br(e) }), ((r) => { t._state === 'writable' && Wr(e), (function (e, r) { e._inFlightWriteRequest._reject(r), e._inFlightWriteRequest = void 0, br(e, r) }(t, r)) })) }(e, t)) } } else _r(r) } function kr(e, r) { e._controlledWritableStream._state === 'writable' && zr(e, r) } function Ar(e) { return jr(e) <= 0 } function zr(e, r) { const t = e._controlledWritableStream; Wr(e), pr(t, r) } function Dr(e) { return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`) } function Ir(e) { return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`) } function Fr(e) { return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`) } function Lr(e) { return new TypeError(`Cannot ${e} a stream using a released writer`) } function Mr(e) { e._closedPromise = c(((r, t) => { e._closedPromise_resolve = r, e._closedPromise_reject = t, e._closedPromiseState = 'pending' })) } function Qr(e, r) { Mr(e), Yr(e, r) } function Yr(e, r) { void 0 !== e._closedPromise_reject && (y(e._closedPromise), e._closedPromise_reject(r), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0, e._closedPromiseState = 'rejected') } function xr(e) { void 0 !== e._closedPromise_resolve && (e._closedPromise_resolve(void 0), e._closedPromise_resolve = void 0, e._closedPromise_reject = void 0, e._closedPromiseState = 'resolved') } function Nr(e) { e._readyPromise = c(((r, t) => { e._readyPromise_resolve = r, e._readyPromise_reject = t })), e._readyPromiseState = 'pending' } function Hr(e, r) { Nr(e), Ur(e, r) } function Vr(e) { Nr(e), Gr(e) } function Ur(e, r) { void 0 !== e._readyPromise_reject && (y(e._readyPromise), e._readyPromise_reject(r), e._readyPromise_resolve = void 0, e._readyPromise_reject = void 0, e._readyPromiseState = 'rejected') } function Gr(e) { void 0 !== e._readyPromise_resolve && (e._readyPromise_resolve(void 0), e._readyPromise_resolve = void 0, e._readyPromise_reject = void 0, e._readyPromiseState = 'fulfilled') }Object.defineProperties(qr.prototype, { abortReason: { enumerable: !0 }, signal: { enumerable: !0 }, error: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(qr.prototype, r.toStringTag, { value: 'WritableStreamDefaultController', configurable: !0 }); const Xr = typeof DOMException !== 'undefined' ? DOMException : void 0; let Jr; const Kr = (function (e) { if (typeof e !== 'function' && typeof e !== 'object') return !1; try { return new e(), !0 } catch (e) { return !1 } }(Xr)) ? Xr : ((Jr = function (e, r) { this.message = e || '', this.name = r || 'Error', Error.captureStackTrace && Error.captureStackTrace(this, this.constructor) }).prototype = Object.create(Error.prototype), Object.defineProperty(Jr.prototype, 'constructor', { value: Jr, writable: !0, configurable: !0 }), Jr); function Zr(e, r, o, n, a, i) { const l = V(e); const u = lr(r); e._disturbed = !0; let s = !1; let m = d(void 0); return c(((v, g) => { let S; let w; let R; let T; if (void 0 !== i) { if (S = function () { const t = new Kr('Aborted', 'AbortError'); const o = []; n || o.push((() => { return r._state === 'writable' ? dr(r, t) : d(void 0) })), a || o.push((() => { return e._state === 'readable' ? Tt(e, t) : d(void 0) })), E((() => { return Promise.all(o.map(((e) => { return e() }))) }), !0, t) }, i.aborted) return void S(); i.addEventListener('abort', S) } if (O(e, l._closedPromise, ((e) => { n ? W(!0, e) : E((() => { return dr(r, e) }), !0, e) })), O(r, u._closedPromise, ((r) => { a ? W(!0, r) : E((() => { return Tt(e, r) }), !0, r) })), w = e, R = l._closedPromise, T = function () { o ? W() : E((() => { return (function (e) { const r = e._ownerWritableStream; const t = r._state; return hr(r) || t === 'closed' ? d(void 0) : t === 'errored' ? f(r._storedError) : Sr(e) }(u)) })) }, w._state === 'closed' ? T() : _(R, T), hr(r) || r._state === 'closed') { const P = new TypeError('the destination writable stream closed before all data could be piped to it'); a ? W(!0, P) : E((() => { return Tt(e, P) }), !0, P) } function q() { const e = m; return b(m, (() => { return e !== m ? q() : void 0 })) } function O(e, r, t) { e._state === 'errored' ? t(e._storedError) : h(r, t) } function E(e, t, o) { function n() { p(e(), (() => { return j(t, o) }), ((e) => { return j(!0, e) })) }s || (s = !0, r._state !== 'writable' || hr(r) ? n() : _(q(), n)) } function W(e, t) { s || (s = !0, r._state !== 'writable' || hr(r) ? j(e, t) : _(q(), (() => { return j(e, t) }))) } function j(e, r) { Tr(u), C(l), void 0 !== i && i.removeEventListener('abort', S), e ? g(r) : v(void 0) }y(c(((e, r) => { !(function o(n) { n ? e() : b(s ? d(!0) : b(u._readyPromise, (() => { return c(((e, r) => { re(l, { _chunkSteps(r) { m = b(Cr(u, r), void 0, t), e(!1) }, _closeSteps() { return e(!0) }, _errorSteps: r }) })) })), o, r) }(!1)) }))) })) } const $r = (function () { function ReadableStreamDefaultController() { throw new TypeError('Illegal constructor') } return Object.defineProperty(ReadableStreamDefaultController.prototype, 'desiredSize', { get() { if (!et(this)) throw ct('desiredSize'); return lt(this) }, enumerable: !1, configurable: !0 }), ReadableStreamDefaultController.prototype.close = function () { if (!et(this)) throw ct('close'); if (!ut(this)) throw new TypeError('The stream is not in a state that permits close'); nt(this) }, ReadableStreamDefaultController.prototype.enqueue = function (e) { if (void 0 === e && (e = void 0), !et(this)) throw ct('enqueue'); if (!ut(this)) throw new TypeError('The stream is not in a state that permits enqueue'); return at(this, e) }, ReadableStreamDefaultController.prototype.error = function (e) { if (void 0 === e && (e = void 0), !et(this)) throw ct('error'); it(this, e) }, ReadableStreamDefaultController.prototype[k] = function (e) { pe(this); const r = this._cancelAlgorithm(e); return ot(this), r }, ReadableStreamDefaultController.prototype[A] = function (e) { const r = this._controlledReadableStream; if (this._queue.length > 0) { const t = fe(this); this._closeRequested && this._queue.length === 0 ? (ot(this), Ct(r)) : rt(this), e._chunkSteps(t) } else U(r, e), rt(this) }, ReadableStreamDefaultController }()); function et(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_controlledReadableStream') && e instanceof $r) } function rt(e) { tt(e) && (e._pulling ? e._pullAgain = !0 : (e._pulling = !0, p(e._pullAlgorithm(), (() => { e._pulling = !1, e._pullAgain && (e._pullAgain = !1, rt(e)) }), ((r) => { it(e, r) })))) } function tt(e) { const r = e._controlledReadableStream; return !!ut(e) && (!!e._started && (!!(Rt(r) && X(r) > 0) || lt(e) > 0)) } function ot(e) { e._pullAlgorithm = void 0, e._cancelAlgorithm = void 0, e._strategySizeAlgorithm = void 0 } function nt(e) { if (ut(e)) { const r = e._controlledReadableStream; e._closeRequested = !0, e._queue.length === 0 && (ot(e), Ct(r)) } } function at(e, r) { if (ut(e)) { const t = e._controlledReadableStream; if (Rt(t) && X(t) > 0)G(t, r, !1); else { let o = void 0; try { o = e._strategySizeAlgorithm(r) } catch (r) { throw it(e, r), r } try { be(e, r, o) } catch (r) { throw it(e, r), r } }rt(e) } } function it(e, r) { const t = e._controlledReadableStream; t._state === 'readable' && (pe(e), ot(e), Pt(t, r)) } function lt(e) { const r = e._controlledReadableStream._state; return r === 'errored' ? null : r === 'closed' ? 0 : e._strategyHWM - e._queueTotalSize } function ut(e) { const r = e._controlledReadableStream._state; return !e._closeRequested && r === 'readable' } function st(e, r, t, o, n, a, i) { r._controlledReadableStream = e, r._queue = void 0, r._queueTotalSize = void 0, pe(r), r._started = !1, r._closeRequested = !1, r._pullAgain = !1, r._pulling = !1, r._strategySizeAlgorithm = i, r._strategyHWM = a, r._pullAlgorithm = o, r._cancelAlgorithm = n, e._readableStreamController = r, p(d(t()), (() => { r._started = !0, rt(r) }), ((e) => { it(r, e) })) } function ct(e) { return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`) } function dt(e, r) { return me(e._readableStreamController) ? (function (e) { let r; let t; let o; let n; let a; let i = V(e); let l = !1; let u = !1; let s = !1; let f = !1; let b = !1; const p = c(((e) => { a = e })); function _(e) { h(e._closedPromise, ((r) => { e === i && (Ae(o._readableStreamController, r), Ae(n._readableStreamController, r), f && b || a(void 0)) })) } function m() { Ue(i) && (C(i), _(i = V(e))), re(i, { _chunkSteps(r) { v((() => { u = !1, s = !1; const t = r; let i = r; if (!f && !b) try { i = de(r) } catch (r) { return Ae(o._readableStreamController, r), Ae(n._readableStreamController, r), void a(Tt(e, r)) }f || ke(o._readableStreamController, t), b || ke(n._readableStreamController, i), l = !1, u ? g() : s && S() })) }, _closeSteps() { l = !1, f || Be(o._readableStreamController), b || Be(n._readableStreamController), o._readableStreamController._pendingPullIntos.length > 0 && Ie(o._readableStreamController, 0), n._readableStreamController._pendingPullIntos.length > 0 && Ie(n._readableStreamController, 0), f && b || a(void 0) }, _errorSteps() { l = !1 } }) } function y(r, t) { ee(i) && (C(i), _(i = Ye(e))); const c = t ? n : o; const d = t ? o : n; Ge(i, r, { _chunkSteps(r) { v((() => { u = !1, s = !1; const o = t ? b : f; if (t ? f : b)o || Fe(c._readableStreamController, r); else { let n = void 0; try { n = de(r) } catch (r) { return Ae(c._readableStreamController, r), Ae(d._readableStreamController, r), void a(Tt(e, r)) }o || Fe(c._readableStreamController, r), ke(d._readableStreamController, n) }l = !1, u ? g() : s && S() })) }, _closeSteps(e) { l = !1; const r = t ? b : f; const o = t ? f : b; r || Be(c._readableStreamController), o || Be(d._readableStreamController), void 0 !== e && (r || Fe(c._readableStreamController, e), !o && d._readableStreamController._pendingPullIntos.length > 0 && Ie(d._readableStreamController, 0)), r && o || a(void 0) }, _errorSteps() { l = !1 } }) } function g() { if (l) return u = !0, d(void 0); l = !0; const e = ze(o._readableStreamController); return e === null ? m() : y(e._view, !1), d(void 0) } function S() { if (l) return s = !0, d(void 0); l = !0; const e = ze(n._readableStreamController); return e === null ? m() : y(e._view, !0), d(void 0) } function w(o) { if (f = !0, r = o, b) { const n = ue([r, t]); const i = Tt(e, n); a(i) } return p } function R(o) { if (b = !0, t = o, f) { const n = ue([r, t]); const i = Tt(e, n); a(i) } return p } function T() {} return o = gt(T, g, w), n = gt(T, S, R), _(i), [o, n] }(e)) : (function (e, r) { let t; let o; let n; let a; let i; const l = V(e); let u = !1; let s = !1; let f = !1; let b = !1; const p = c(((e) => { i = e })); function _() { return u ? (s = !0, d(void 0)) : (u = !0, re(l, { _chunkSteps(e) { v((() => { s = !1; const r = e; const t = e; f || at(n._readableStreamController, r), b || at(a._readableStreamController, t), u = !1, s && _() })) }, _closeSteps() { u = !1, f || nt(n._readableStreamController), b || nt(a._readableStreamController), f && b || i(void 0) }, _errorSteps() { u = !1 } }), d(void 0)) } function m(r) { if (f = !0, t = r, b) { const n = ue([t, o]); const a = Tt(e, n); i(a) } return p } function y(r) { if (b = !0, o = r, f) { const n = ue([t, o]); const a = Tt(e, n); i(a) } return p } function g() {} return n = vt(g, _, m), a = vt(g, _, y), h(l._closedPromise, ((e) => { it(n._readableStreamController, e), it(a._readableStreamController, e), f && b || i(void 0) })), [n, a] }(e)) } function ft(e, r, t) { return F(e, t), function (t) { return S(e, r, [t]) } } function bt(e, r, t) { return F(e, t), function (t) { return S(e, r, [t]) } } function pt(e, r, t) { return F(e, t), function (t) { return g(e, r, [t]) } } function _t(e, r) { if ((e = `${e}`) !== 'bytes') throw new TypeError(`${r} '${e}' is not a valid enumeration value for ReadableStreamType`); return e } function ht(e, r) { if ((e = `${e}`) !== 'byob') throw new TypeError(`${r} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`); return e } function mt(e, r) { I(e, r); const t = e == null ? void 0 : e.preventAbort; const o = e == null ? void 0 : e.preventCancel; const n = e == null ? void 0 : e.preventClose; const a = e == null ? void 0 : e.signal; return void 0 !== a && (function (e, r) { if (!(function (e) { if (typeof e !== 'object' || e === null) return !1; try { return typeof e.aborted === 'boolean' } catch (e) { return !1 } }(e))) throw new TypeError(`${r} is not an AbortSignal.`) }(a, `${r} has member 'signal' that`)), { preventAbort: Boolean(t), preventCancel: Boolean(o), preventClose: Boolean(n), signal: a } }Object.defineProperties($r.prototype, { close: { enumerable: !0 }, enqueue: { enumerable: !0 }, error: { enumerable: !0 }, desiredSize: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty($r.prototype, r.toStringTag, { value: 'ReadableStreamDefaultController', configurable: !0 }); const yt = (function () { function ReadableStream(e, r) { void 0 === e && (e = {}), void 0 === r && (r = {}), void 0 === e ? e = null : L(e, 'First parameter'); const t = Ze(r, 'Second parameter'); const o = (function (e, r) { I(e, r); const t = e; const o = t == null ? void 0 : t.autoAllocateChunkSize; const n = t == null ? void 0 : t.cancel; const a = t == null ? void 0 : t.pull; const i = t == null ? void 0 : t.start; const l = t == null ? void 0 : t.type; return { autoAllocateChunkSize: void 0 === o ? void 0 : N(o, `${r} has member 'autoAllocateChunkSize' that`), cancel: void 0 === n ? void 0 : ft(n, t, `${r} has member 'cancel' that`), pull: void 0 === a ? void 0 : bt(a, t, `${r} has member 'pull' that`), start: void 0 === i ? void 0 : pt(i, t, `${r} has member 'start' that`), type: void 0 === l ? void 0 : _t(l, `${r} has member 'type' that`) } }(e, 'First parameter')); if (St(this), o.type === 'bytes') { if (void 0 !== t.size) throw new RangeError('The strategy for a byte stream cannot have a size function'); !(function (e, r, t) { const o = Object.create(he.prototype); let n = function () {}; let a = function () { return d(void 0) }; let i = function () { return d(void 0) }; void 0 !== r.start && (n = function () { return r.start(o) }), void 0 !== r.pull && (a = function () { return r.pull(o) }), void 0 !== r.cancel && (i = function (e) { return r.cancel(e) }); const l = r.autoAllocateChunkSize; if (l === 0) throw new TypeError('autoAllocateChunkSize must be greater than 0'); Le(e, o, n, a, i, t, l) }(this, o, Je(t, 0))) } else { const n = Ke(t); !(function (e, r, t, o) { const n = Object.create($r.prototype); let a = function () {}; let i = function () { return d(void 0) }; let l = function () { return d(void 0) }; void 0 !== r.start && (a = function () { return r.start(n) }), void 0 !== r.pull && (i = function () { return r.pull(n) }), void 0 !== r.cancel && (l = function (e) { return r.cancel(e) }), st(e, n, a, i, l, t, o) }(this, o, Je(t, 1), n)) } } return Object.defineProperty(ReadableStream.prototype, 'locked', { get() { if (!wt(this)) throw qt('locked'); return Rt(this) }, enumerable: !1, configurable: !0 }), ReadableStream.prototype.cancel = function (e) { return void 0 === e && (e = void 0), wt(this) ? Rt(this) ? f(new TypeError('Cannot cancel a stream that already has a reader')) : Tt(this, e) : f(qt('cancel')) }, ReadableStream.prototype.getReader = function (e) { if (void 0 === e && (e = void 0), !wt(this)) throw qt('getReader'); return void 0 === (function (e, r) { I(e, r); const t = e == null ? void 0 : e.mode; return { mode: void 0 === t ? void 0 : ht(t, `${r} has member 'mode' that`) } }(e, 'First parameter')).mode ? V(this) : Ye(this) }, ReadableStream.prototype.pipeThrough = function (e, r) { if (void 0 === r && (r = {}), !wt(this)) throw qt('pipeThrough'); M(e, 1, 'pipeThrough'); const t = (function (e, r) { I(e, r); const t = e == null ? void 0 : e.readable; Q(t, 'readable', 'ReadableWritablePair'), H(t, `${r} has member 'readable' that`); const o = e == null ? void 0 : e.writable; return Q(o, 'writable', 'ReadableWritablePair'), nr(o, `${r} has member 'writable' that`), { readable: t, writable: o } }(e, 'First parameter')); const o = mt(r, 'Second parameter'); if (Rt(this)) throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream'); if (cr(t.writable)) throw new TypeError('ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream'); return y(Zr(this, t.writable, o.preventClose, o.preventAbort, o.preventCancel, o.signal)), t.readable }, ReadableStream.prototype.pipeTo = function (e, r) { if (void 0 === r && (r = {}), !wt(this)) return f(qt('pipeTo')); if (void 0 === e) return f("Parameter 1 is required in 'pipeTo'."); if (!sr(e)) return f(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream")); let t; try { t = mt(r, 'Second parameter') } catch (e) { return f(e) } return Rt(this) ? f(new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream')) : cr(e) ? f(new TypeError('ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream')) : Zr(this, e, t.preventClose, t.preventAbort, t.preventCancel, t.signal) }, ReadableStream.prototype.tee = function () { if (!wt(this)) throw qt('tee'); return ue(dt(this)) }, ReadableStream.prototype.values = function (e) { if (void 0 === e && (e = void 0), !wt(this)) throw qt('values'); let r; let t; let o; let n; let a; const i = (function (e, r) { I(e, r); const t = e == null ? void 0 : e.preventCancel; return { preventCancel: Boolean(t) } }(e, 'First parameter')); return r = this, t = i.preventCancel, o = V(r), n = new oe(o, t), (a = Object.create(ne))._asyncIteratorImpl = n, a }, ReadableStream }()); function vt(e, r, t, o, n) { void 0 === o && (o = 1), void 0 === n && (n = function () { return 1 }); const a = Object.create(yt.prototype); return St(a), st(a, Object.create($r.prototype), e, r, t, o, n), a } function gt(e, r, t) { const o = Object.create(yt.prototype); return St(o), Le(o, Object.create(he.prototype), e, r, t, 0, void 0), o } function St(e) { e._state = 'readable', e._reader = void 0, e._storedError = void 0, e._disturbed = !1 } function wt(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_readableStreamController') && e instanceof yt) } function Rt(e) { return void 0 !== e._reader } function Tt(e, r) { if (e._disturbed = !0, e._state === 'closed') return d(void 0); if (e._state === 'errored') return f(e._storedError); Ct(e); const o = e._reader; return void 0 !== o && Ue(o) && (o._readIntoRequests.forEach(((e) => { e._closeSteps(void 0) })), o._readIntoRequests = new w()), m(e._readableStreamController[k](r), t) } function Ct(e) { e._state = 'closed'; const r = e._reader; void 0 !== r && (W(r), ee(r) && (r._readRequests.forEach(((e) => { e._closeSteps() })), r._readRequests = new w())) } function Pt(e, r) { e._state = 'errored', e._storedError = r; const t = e._reader; void 0 !== t && (E(t, r), ee(t) ? (t._readRequests.forEach(((e) => { e._errorSteps(r) })), t._readRequests = new w()) : (t._readIntoRequests.forEach(((e) => { e._errorSteps(r) })), t._readIntoRequests = new w())) } function qt(e) { return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`) } function Ot(e, r) { I(e, r); const t = e == null ? void 0 : e.highWaterMark; return Q(t, 'highWaterMark', 'QueuingStrategyInit'), { highWaterMark: Y(t) } }Object.defineProperties(yt.prototype, { cancel: { enumerable: !0 }, getReader: { enumerable: !0 }, pipeThrough: { enumerable: !0 }, pipeTo: { enumerable: !0 }, tee: { enumerable: !0 }, values: { enumerable: !0 }, locked: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(yt.prototype, r.toStringTag, { value: 'ReadableStream', configurable: !0 }), typeof r.asyncIterator === 'symbol' && Object.defineProperty(yt.prototype, r.asyncIterator, { value: yt.prototype.values, writable: !0, configurable: !0 }); const Et = function (e) { return e.byteLength }; try { Object.defineProperty(Et, 'name', { value: 'size', configurable: !0 }) } catch (K) {} const Wt = (function () { function ByteLengthQueuingStrategy(e) { M(e, 1, 'ByteLengthQueuingStrategy'), e = Ot(e, 'First parameter'), this._byteLengthQueuingStrategyHighWaterMark = e.highWaterMark } return Object.defineProperty(ByteLengthQueuingStrategy.prototype, 'highWaterMark', { get() { if (!Bt(this)) throw jt('highWaterMark'); return this._byteLengthQueuingStrategyHighWaterMark }, enumerable: !1, configurable: !0 }), Object.defineProperty(ByteLengthQueuingStrategy.prototype, 'size', { get() { if (!Bt(this)) throw jt('size'); return Et }, enumerable: !1, configurable: !0 }), ByteLengthQueuingStrategy }()); function jt(e) { return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`) } function Bt(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_byteLengthQueuingStrategyHighWaterMark') && e instanceof Wt) }Object.defineProperties(Wt.prototype, { highWaterMark: { enumerable: !0 }, size: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(Wt.prototype, r.toStringTag, { value: 'ByteLengthQueuingStrategy', configurable: !0 }); const kt = function () { return 1 }; try { Object.defineProperty(kt, 'name', { value: 'size', configurable: !0 }) } catch (K) {} const At = (function () { function CountQueuingStrategy(e) { M(e, 1, 'CountQueuingStrategy'), e = Ot(e, 'First parameter'), this._countQueuingStrategyHighWaterMark = e.highWaterMark } return Object.defineProperty(CountQueuingStrategy.prototype, 'highWaterMark', { get() { if (!Dt(this)) throw zt('highWaterMark'); return this._countQueuingStrategyHighWaterMark }, enumerable: !1, configurable: !0 }), Object.defineProperty(CountQueuingStrategy.prototype, 'size', { get() { if (!Dt(this)) throw zt('size'); return kt }, enumerable: !1, configurable: !0 }), CountQueuingStrategy }()); function zt(e) { return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`) } function Dt(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_countQueuingStrategyHighWaterMark') && e instanceof At) } function It(e, r, t) { return F(e, t), function (t) { return S(e, r, [t]) } } function Ft(e, r, t) { return F(e, t), function (t) { return g(e, r, [t]) } } function Lt(e, r, t) { return F(e, t), function (t, o) { return S(e, r, [t, o]) } }Object.defineProperties(At.prototype, { highWaterMark: { enumerable: !0 }, size: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(At.prototype, r.toStringTag, { value: 'CountQueuingStrategy', configurable: !0 }); const Mt = (function () { function TransformStream(e, r, t) { void 0 === e && (e = {}), void 0 === r && (r = {}), void 0 === t && (t = {}), void 0 === e && (e = null); const o = Ze(r, 'Second parameter'); const n = Ze(t, 'Third parameter'); const a = (function (e, r) { I(e, r); const t = e == null ? void 0 : e.flush; const o = e == null ? void 0 : e.readableType; const n = e == null ? void 0 : e.start; const a = e == null ? void 0 : e.transform; const i = e == null ? void 0 : e.writableType; return { flush: void 0 === t ? void 0 : It(t, e, `${r} has member 'flush' that`), readableType: o, start: void 0 === n ? void 0 : Ft(n, e, `${r} has member 'start' that`), transform: void 0 === a ? void 0 : Lt(a, e, `${r} has member 'transform' that`), writableType: i } }(e, 'First parameter')); if (void 0 !== a.readableType) throw new RangeError('Invalid readableType specified'); if (void 0 !== a.writableType) throw new RangeError('Invalid writableType specified'); let i; const l = Je(n, 0); const u = Ke(n); const s = Je(o, 1); const b = Ke(o); !(function (e, r, t, o, n, a) { function i() { return r } function l(r) { return (function (e, r) { const t = e._transformStreamController; if (e._backpressure) { return m(e._backpressureChangePromise, (() => { const o = e._writable; if (o._state === 'erroring') throw o._storedError; return Xt(t, r) })) } return Xt(t, r) }(e, r)) } function u(r) { return (function (e, r) { return Yt(e, r), d(void 0) }(e, r)) } function s() { return (function (e) { const r = e._readable; const t = e._transformStreamController; const o = t._flushAlgorithm(); return Ut(t), m(o, (() => { if (r._state === 'errored') throw r._storedError; nt(r._readableStreamController) }), ((t) => { throw Yt(e, t), r._storedError })) }(e)) } function c() { return (function (e) { return Nt(e, !1), e._backpressureChangePromise }(e)) } function f(r) { return xt(e, r), d(void 0) }e._writable = (function (e, r, t, o, n, a) { void 0 === n && (n = 1), void 0 === a && (a = function () { return 1 }); const i = Object.create(ir.prototype); return ur(i), Er(i, Object.create(qr.prototype), e, r, t, o, n, a), i }(i, l, s, u, t, o)), e._readable = vt(i, c, f, n, a), e._backpressure = void 0, e._backpressureChangePromise = void 0, e._backpressureChangePromise_resolve = void 0, Nt(e, !0), e._transformStreamController = void 0 }(this, c(((e) => { i = e })), s, b, l, u)), (function (e, r) { const t = Object.create(Ht.prototype); let o = function (e) { try { return Gt(t, e), d(void 0) } catch (e) { return f(e) } }; let n = function () { return d(void 0) }; void 0 !== r.transform && (o = function (e) { return r.transform(e, t) }); void 0 !== r.flush && (n = function () { return r.flush(t) }); !(function (e, r, t, o) { r._controlledTransformStream = e, e._transformStreamController = r, r._transformAlgorithm = t, r._flushAlgorithm = o }(e, t, o, n)) }(this, a)), void 0 !== a.start ? i(a.start(this._transformStreamController)) : i(void 0) } return Object.defineProperty(TransformStream.prototype, 'readable', { get() { if (!Qt(this)) throw Kt('readable'); return this._readable }, enumerable: !1, configurable: !0 }), Object.defineProperty(TransformStream.prototype, 'writable', { get() { if (!Qt(this)) throw Kt('writable'); return this._writable }, enumerable: !1, configurable: !0 }), TransformStream }()); function Qt(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_transformStreamController') && e instanceof Mt) } function Yt(e, r) { it(e._readable._readableStreamController, r), xt(e, r) } function xt(e, r) { Ut(e._transformStreamController), kr(e._writable._writableStreamController, r), e._backpressure && Nt(e, !1) } function Nt(e, r) { void 0 !== e._backpressureChangePromise && e._backpressureChangePromise_resolve(), e._backpressureChangePromise = c(((r) => { e._backpressureChangePromise_resolve = r })), e._backpressure = r }Object.defineProperties(Mt.prototype, { readable: { enumerable: !0 }, writable: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(Mt.prototype, r.toStringTag, { value: 'TransformStream', configurable: !0 }); var Ht = (function () { function TransformStreamDefaultController() { throw new TypeError('Illegal constructor') } return Object.defineProperty(TransformStreamDefaultController.prototype, 'desiredSize', { get() { if (!Vt(this)) throw Jt('desiredSize'); return lt(this._controlledTransformStream._readable._readableStreamController) }, enumerable: !1, configurable: !0 }), TransformStreamDefaultController.prototype.enqueue = function (e) { if (void 0 === e && (e = void 0), !Vt(this)) throw Jt('enqueue'); Gt(this, e) }, TransformStreamDefaultController.prototype.error = function (e) { if (void 0 === e && (e = void 0), !Vt(this)) throw Jt('error'); let r; r = e, Yt(this._controlledTransformStream, r) }, TransformStreamDefaultController.prototype.terminate = function () { if (!Vt(this)) throw Jt('terminate'); !(function (e) { const r = e._controlledTransformStream; nt(r._readable._readableStreamController); const t = new TypeError('TransformStream terminated'); xt(r, t) }(this)) }, TransformStreamDefaultController }()); function Vt(e) { return !!n(e) && (!!Object.prototype.hasOwnProperty.call(e, '_controlledTransformStream') && e instanceof Ht) } function Ut(e) { e._transformAlgorithm = void 0, e._flushAlgorithm = void 0 } function Gt(e, r) { const t = e._controlledTransformStream; const o = t._readable._readableStreamController; if (!ut(o)) throw new TypeError('Readable side is not in a state that permits enqueue'); try { at(o, r) } catch (e) { throw xt(t, e), t._readable._storedError }(function (e) { return !tt(e) }(o)) !== t._backpressure && Nt(t, !0) } function Xt(e, r) { return m(e._transformAlgorithm(r), void 0, ((r) => { throw Yt(e._controlledTransformStream, r), r })) } function Jt(e) { return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`) } function Kt(e) { return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`) }Object.defineProperties(Ht.prototype, { enqueue: { enumerable: !0 }, error: { enumerable: !0 }, terminate: { enumerable: !0 }, desiredSize: { enumerable: !0 } }), typeof r.toStringTag === 'symbol' && Object.defineProperty(Ht.prototype, r.toStringTag, { value: 'TransformStreamDefaultController', configurable: !0 }); const Zt = { ReadableStream: yt, ReadableStreamDefaultController: $r, ReadableByteStreamController: he, ReadableStreamBYOBRequest: _e, ReadableStreamDefaultReader: $, ReadableStreamBYOBReader: Ve, WritableStream: ir, WritableStreamDefaultController: qr, WritableStreamDefaultWriter: vr, ByteLengthQueuingStrategy: Wt, CountQueuingStrategy: At, TransformStream: Mt, TransformStreamDefaultController: Ht }; if (void 0 !== o) for (const $t in Zt)Object.prototype.hasOwnProperty.call(Zt, $t) && Object.defineProperty(o, $t, { value: Zt[$t], writable: !0, configurable: !0 }); e.ByteLengthQueuingStrategy = Wt, e.CountQueuingStrategy = At, e.ReadableByteStreamController = he, e.ReadableStream = yt, e.ReadableStreamBYOBReader = Ve, e.ReadableStreamBYOBRequest = _e, e.ReadableStreamDefaultController = $r, e.ReadableStreamDefaultReader = $, e.TransformStream = Mt, e.TransformStreamDefaultController = Ht, e.WritableStream = ir, e.WritableStreamDefaultController = qr, e.WritableStreamDefaultWriter = vr, Object.defineProperty(e, '__esModule', { value: !0 })
})))
// # sourceMappingURL=polyfill.min.js.map
