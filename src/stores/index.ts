import { create } from 'zustand'
import createPageContainerStore, { PageContainerState } from './PageContainer'

/**
 * 后续需要添加其他模块的全局 store，并且有类型提示的话参考以下步骤：
 * 1. 在 types 中创建模块 store types, 并与原先的合并，例如：<PageContainerState> → <PageContainerState & TestState>
 * 2. 新模块 store 中 ts 类型相关代码编写参考 PageContainerStore 模块
 *
 * 参考地址：https://docs.pmnd.rs/zustand/guides/typescript#slices-pattern
 */
export const useStore = create<PageContainerState>()((...props) => ({
  ...createPageContainerStore(...props),
}))
