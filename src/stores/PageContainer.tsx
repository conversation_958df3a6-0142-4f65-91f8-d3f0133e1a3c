import { StateCreator } from 'zustand'

export type PageContainerState = {
  extra: any
  title: any
  footer: any
  needBack: boolean
  closely: boolean
  pageKeepCache: Record<string, any>
  setExtra: (extra: any) => void
  setTitle: (title: any) => void
  setFooter: (footer: any) => void
  setNeedBack: (needBack?: boolean) => void
  setClosely: (closely?: boolean) => void
  setPageKeepCache: (path: string, value: any) => void
}

const createPageContainerStore: StateCreator<PageContainerState, [], [], PageContainerState> = (
  set,
) => ({
  extra: [],
  title: [],
  footer: [],
  needBack: false,
  closely: false,
  pageKeepCache: {},
  setExtra: (extra: any = []) => {
    set({
      extra,
    })
  },
  setTitle: (title: any = []) => {
    set({
      title,
    })
  },
  setFooter: (footer: any = []) => {
    set({
      footer,
    })
  },
  setNeedBack: (needBack: any = false) => {
    set({
      needBack,
    })
  },
  setClosely: (closely: any = false) => {
    set({
      closely,
    })
  },
  setPageKeepCache: (path, value) => {
    set((state) => ({
      pageKeepCache: { ...state.pageKeepCache, [path]: value },
    }))
  },
})

export default createPageContainerStore
