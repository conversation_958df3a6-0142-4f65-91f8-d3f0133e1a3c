import React, { useEffect, useState, useMemo, useRef } from 'react'
import modal<PERSON>rapperHoc from '@/components/modalWrapperHoc'
import ExportModal from '@/components/ExportModal'
import { SchemaTable } from '@amazebird/antd-schema-table'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import { getDminStaffRelationV1SecurityEventQuery as getSecurityList } from '@/api/securityEventAdminController'
import { useUcPermissionDict } from '@/hooks/useUcPermissionDict'
import { EXPORT_TASK_TYPE, UC_DICT_KEY } from '@/constants'
import { map } from 'lodash-es'
import PageLoading from '@/components/pageLoading'
import { useLoaderContext } from '@/components/Loader/useLoading'
import { exportService } from '@/services/export'
import { message } from 'antd'
import BusinessPermission from '@/components/businessPermission'
import { SECURITY } from '@/constants/rbac-code/security'
import { getColumns, getSearchColumns } from './columns'
import style from './style.module.less'

const Security: React.FC = () => {
  const [pageLoading, setPageLoading] = useState(true)
  const action = SchemaTable.createAction()
  const form: any = SchemaForm.createForm()
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()
  const { loader } = useLoaderContext()
  const { data: ucDict, isLoading: ucLoading } = useUcPermissionDict({
    authKey: [
      UC_DICT_KEY.SECURITY_AUTH_FIELD,
      UC_DICT_KEY.BM_HTZT,
      UC_DICT_KEY.BM_BZGSQY,
      UC_DICT_KEY.DEPT_AUTH,
    ],
  })
  const columnState = useRef<Record<string, any> | undefined>(undefined)

  useEffect(() => {
    if (!ehrLoading) {
      form.setState({ ehrDict })
    }
    if (!ehrLoading && !dhrLoading && !ucLoading) {
      console.log(ucDict, ehrDict, dhrDict)
      setPageLoading(false)
    }
  }, [ehrLoading, dhrLoading, ucLoading])

  const columns = useMemo(() => {
    return getColumns({ ehrDict, action })
  }, [ehrDict])

  const searchColumns = useMemo(() => {
    return getSearchColumns({ ucDict })
  }, [ucDict])

  const exportData = () => {
    const searchValues = form.getFieldsValue()
    modalWrapperHoc(ExportModal)({
      searchColumns,
      searchValues,
      excludeField: ['timeAccident'],
      onExport: async (params) => {
        const {
          accidentAreas,
          accidentContractSubjects,
          departmentId,
          timeAccident,
          process,
          ...rest
        } = params
        const exportParams = {
          accidentAreas: map(accidentAreas, (item) => item.value),
          accidentContractSubjects: map(accidentContractSubjects, (item) => item.value),
          departmentId: departmentId?.value,
          timeAccidentStart: timeAccident
            ? params.timeAccident[0].startOf('month').valueOf()
            : undefined,
          timeAccidentEnd: timeAccident
            ? params.timeAccident[1].endOf('month').valueOf()
            : undefined,
          process: process ? process.value : undefined,
          ...rest,
        }
        loader?.show('正在导出数据')
        const res = await exportService.export(EXPORT_TASK_TYPE.SECURITY, exportParams)
        if (res.success) {
          message.success('数据导出成功')
        } else {
          message.error(res.errMsg || '数据导出失败')
        }
        loader?.hide()
      },
    })
  }

  const requestData = async ({ filter, pagination }) => {
    try {
      const {
        accidentAreas,
        accidentContractSubjects,
        departmentId,
        timeAccident,
        process,
        ...rest
      } = filter
      const { current, pageSize } = pagination
      const params = {
        page: current,
        size: pageSize,
        accidentAreas: map(accidentAreas, (item) => item.value),
        accidentContractSubjects: map(accidentContractSubjects, (item) => item.value),
        departmentId: departmentId?.value,
        timeAccidentStart: timeAccident ? timeAccident[0].startOf('month').valueOf() : undefined,
        timeAccidentEnd: timeAccident ? timeAccident[1].endOf('month').valueOf() : undefined,
        process: process ? process.value : undefined,
        sort: ['time_create,desc'],
        ...rest,
      }
      const { data, count } = await getSecurityList(params)
      return {
        success: true,
        data,
        total: count,
      }
    } catch (error) {
      return {
        data: [],
        total: 0,
      }
    }
  }

  if (pageLoading) {
    return <PageLoading style={{ minHeight: 'calc(100vh - 300px)', backgroundColor: '#fff' }} />
  }

  return (
    <div className={style.table}>
      <SchemaTable
        columns={columns}
        form={form}
        action={action}
        searchColumns={searchColumns}
        request={requestData}
        rowKey="id"
        toolbarOptions={{ setting: true }}
        columnsState={{
          persistenceType: 'localStorage',
          persistenceKey: 'security-table-config',
          onChange: (value) => {
            columnState.current = value
          },
        }}
        toolbar={{
          action: (
            <BusinessPermission code={SECURITY.EXPORT}>
              <a onClick={exportData}>导出</a>
            </BusinessPermission>
          ),
        }}
      />
    </div>
  )
}

export default Security
