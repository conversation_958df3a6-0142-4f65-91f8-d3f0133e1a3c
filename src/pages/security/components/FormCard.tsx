import React, { ReactNode } from 'react'
import { Card } from 'antd'
import { Item, FormItemGrid } from '@amazebird/antd-schema-form'

interface FormField {
  /** 字段名 */
  name: string
  /** 字段配置 */
  config?: any
}

interface FormCardProps {
  /** 卡片标题 */
  title: string
  /** 字段列表 */
  fields: FormField[]
  /** 生成表单项配置的函数 */
  generateFormItemConfig: (name: string) => any
  /** 列数 */
  colCount?: number
  /** 是否填充空白 */
  fillEmpty?: boolean
  /** 卡片大小 */
  size?: 'default' | 'small'
  /** 额外的操作区域 */
  extra?: ReactNode
  /** 自定义内容 */
  children?: ReactNode
}

const FormCard: React.FC<FormCardProps> = ({
  title,
  fields,
  generateFormItemConfig,
  colCount = 2,
  fillEmpty = true,
  size = 'default',
  extra,
  children,
}) => {
  return (
    <Card title={title} size={size} extra={extra}>
      {fields.length > 0 && (
        <FormItemGrid fillEmpty={fillEmpty} colCount={colCount}>
          {fields.map((field) => (
            <Item key={field.name} {...generateFormItemConfig(field.name)} {...field.config} />
          ))}
        </FormItemGrid>
      )}
      {children}
    </Card>
  )
}

export default FormCard
