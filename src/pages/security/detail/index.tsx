import React from 'react'
import { renderStateCenterCtx, StateCenter } from '@amazebird/utils'
import { useUcPermissionDict } from '@/hooks/useUcPermissionDict'
import { UC_DICT_KEY } from '@/constants'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import Schedule from './components/Schedule'

export default function index() {
  const { data: ehrDict } = useEhrDicts()
  const { data: dhrDict } = useDhrDicts()
  const { data: ucDict, isLoading } = useUcPermissionDict({
    authKey: [UC_DICT_KEY.SECURITY_AUTH_FIELD, UC_DICT_KEY.DEPT_AUTH],
  })

  const bind = [
    {
      name: 'Schedule',
      fields: [
        'contact',
        'positionId',
        'positionType',
        'departmentLevelFourth',
        'timeEntry',
        'timeEventCreated',
        'userIdUpdate',
        'timeUpdate',
        'oaNo',
        'id',
        'timeIncidentYearMonthCopy',
        'accidentContent',
        'accidentNatureName',
        'accidentScenario',
        'alarmUser',
        'claimPaidDate',
        'closeDate',
        'companyRepayAmount',
        'completeClaimMaterialPostDate',
        'currentDebt',
        'deliveryOrderNum',
        'disabilityLevel',
        'employeeRepayAmount',
        'employeeTotalDebt',
        'eventCoefficientChangeReason',
        'eventCoefficientName',
        'eventLevelName',
        'injuredPersonnelCondition',
        'insuranceRepayAmount',
        'insureType',
        'isApplyLaborAbilityIdentification',
        'isApplyWorkInjuryCompensation',
        'isApplyWorkInjuryRecognition',
        'isBorrow',
        'isStaffPenalty',
        'isSuspend',
        'isThirdPartyInvolved',
        'isWorkInjury',
        'notImplementedRewardPunishReason',
        'penaltyTypeName',
        'process',
        'processRemark',
        'recoverAmount',
        'recoverMethod',
        'recoveredAmount',
        'remark',
        'remarkOther',
        'remarkOtherTwo',
        'repayProgress',
        'reportUnit',
        'responsibilityAllocationName',
        'rewardPunishCategory',
        'rewardPunishDate',
        'rewardPunishProgress',
        'rewardPunishType',
        'staffInjuryType',
        'startRecoverDate',
        'suspendDate',
        'suspendReason',
        'thirdPartyAge',
        'thirdPartyContact',
        'thirdPartyInjuryType',
        'thirdPartyName',
        'thirdPartyPropertyLoss',
        'thirdPartyVehicleLoss',
        'timeAccident',
        'totalBorrowAmount',
        'unclosedReason',
        'userIdResponsibleManager',
        'weatherName',
        'withdrawReason',
        'timeIncidentYearMonth',
        'accidentDurationInCompany',
        'accidentDurationCategory',
        'accidentAreaName',
        'eventLocation',
        'dateCategoryName',
        'eventCategoryName',
        'followCycleCategory',
        'accidentContractSubject',
        'accidentContractTypeName',
        'currentContractSubject',
        'currentContractType',
        'accidentDepartmentId',
        'accidentPositionId',
        'followPeriod',
        'followPeriodCategory',
        'is_staff_penalty',
        'noPenaltyReasonDetails',
        'noPenaltyReason',
        'attachment',
      ],
    },
  ]
  const stateCenter = new StateCenter({
    bind,
  })

  if (isLoading) {
    return <div />
  }

  return renderStateCenterCtx(
    stateCenter,
    <>
      <Schedule dict={{ ehrDict, dhrDict, ucDict }} />
    </>,
  )
}
