import React from 'react'
import { Table, Typography, Card } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import styles from './index.module.less'

const { Text } = Typography

interface LoanRecord {
  key: string
  loanDate: string
  repaymentPlan: string
  salaryDeductionStartMonth: string
  loanAmount: number
}

interface IProps {
  data?: LoanRecord[]
}

const AdvanceManagementTable: React.FC<IProps> = ({ data }) => {
  // 模拟数据
  const dataSource: LoanRecord[] = [
    {
      key: '1',
      loanDate: '2024-09-21',
      repaymentPlan: '工资月还',
      salaryDeductionStartMonth: '2025-06',
      loanAmount: 10000.0,
    },
    {
      key: '2',
      loanDate: '2022-09-01',
      repaymentPlan: '一次性回款',
      salaryDeductionStartMonth: '--',
      loanAmount: 5000.0,
    },
    {
      key: '3',
      loanDate: '2020-09-01',
      repaymentPlan: '一次性回款',
      salaryDeductionStartMonth: '--',
      loanAmount: 10000.0,
    },
  ]

  // 计算总金额
  const totalAmount = dataSource.reduce((sum, record) => sum + record.loanAmount, 0)

  // 表格列定义
  const columns: ColumnsType<LoanRecord> = [
    {
      title: '借款日期',
      dataIndex: 'loanDate',
      key: 'loanDate',
      width: 140,
    },
    {
      title: '还款计划',
      dataIndex: 'repaymentPlan',
      key: 'repaymentPlan',
      width: 140,
    },
    {
      title: '薪资扣款开始月份',
      dataIndex: 'salaryDeductionStartMonth',
      key: 'salaryDeductionStartMonth',
      width: 160,
    },
    {
      title: '借款金额（元）',
      dataIndex: 'loanAmount',
      key: 'loanAmount',
      width: 140,
      align: 'right',
      render: (amount: number) => amount.toFixed(2),
    },
  ]

  return (
    <Card bodyStyle={{ padding: 0 }}>
      {/* 员工借款总额标头 */}
      <div className={styles.headerSection}>
        <Text>员工借款总额</Text>
        <Text>
          {totalAmount.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </Text>
      </div>
      <Table
        dataSource={data || dataSource}
        columns={columns}
        pagination={false}
        size="small"
        showHeader
      />
    </Card>
  )
}

export default AdvanceManagementTable
