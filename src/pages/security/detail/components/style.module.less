.scheduleForm {
  max-height: calc(100vh - 350px);

  .subTitle {
    font-weight: bold;
    margin-bottom: 16px;
  }

  .employeeDetail {
    background-color: #fff;

    :global {
      .@{ant-prefix}-form-item {
        margin-bottom: 0px;
      }
    }

    .lastCol {
      margin-bottom: 24px;
    }
  }

  .anchorWrapper {
    padding: 16px;

    :global {
      .editor_Anchor_anchors {
        height: calc(100vh - 355px);
      }

      .editor_Anchor_anchorContextBox {
        background-color: initial;
        padding: 0;
      }

      .editor_Anchor_anchorItemBox {
        margin-bottom: 16px;
      }

      .@{ant-prefix}-form-item {
        .@{ant-prefix}-picker {
          width: 100%;
        }
        .@{ant-prefix}-input-number-group-wrapper {
          width: 100%;
        }
      }
    }

    [data-type='AnchorItem']:last-child {
      margin-bottom: 0;
    }
  }
}
