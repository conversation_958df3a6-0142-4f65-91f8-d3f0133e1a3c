import React, { useEffect, useState, useMemo } from 'react'
import { SchemaForm, Item, FormItemGrid } from '@amazebird/antd-schema-form'
import { Row, Col, Card, Button, Space, message, Divider, Dropdown, Tag } from 'antd'
import { Anchor as DhrAnchor } from '@amazebird/editor-component'
import { map, pickBy, pick, keys } from 'lodash-es'
import {
  getDminStaffRelationV1SecurityEventById as getDetailById,
  putDminStaffRelationV1SecurityEventById as editDetail,
} from '@/api/securityEventAdminController'
import qs from 'query-string'
import '@amazebird/antd-business-field'
import { useNavigate } from 'react-router-dom'
import Footer from '@/components/baseContainer/Footer'
import moment from 'moment'
import PageLoading from '@/components/pageLoading'
import useBatchPackDownload from '@/hooks/useBatchPackDownload'
import { useStore } from '@/stores'
import {
  generateFormItemConfig,
  scrollToFirstErrorField,
  generateFormItemClass,
} from '@/pages/employment-dispute/components/DisputeForm/utils'
import BusinessPermission from '@/components/businessPermission'
import { SECURITY } from '@/constants/rbac-code/security'
import { DownOutlined } from '@ant-design/icons'
import InvoiceTable from '@/components/InvoiceTable'
import { SECURITY_EVENT_PROCESS_COLOR, SECURITY_EVENT_PROCESS_NAME } from '@/constants'
import { useSecurityOperations } from '../../hooks/useSecurityOperations'
import { canUpgradeToDispute, isInProgress, isWithdrawn } from '../../TableAction'
import { getSchema } from './schema'
import style from './style.module.less'
import AdvanceManagementTable from './AdvanceManagementTable'
import RepaymentManagementTable from './RepaymentManagementTable'

interface Iprops {
  dict: any
}

const Schedule = (props: Iprops) => {
  const { dict } = props
  const { dhrDict, ehrDict, ucDict } = dict
  const { id } = qs.parse(window.location.search)
  const form: any = SchemaForm.createForm()
  const navigate = useNavigate()
  const [footer, setFooter] = useState<React.ReactNode>(null)
  const [loading, setLoading] = useState(true)
  const [commitLoading, setCommitLoading] = useState(false)
  const [securityDetail, setSecurityDetail] = useState<any>()
  const [hasAttachment, setHasAttachment] = useState(false)
  const { batchPackDownload } = useBatchPackDownload()
  const setTitle = useStore((state) => state.setTitle)
  const setExtraElement = useStore((state) => state.setExtra)
  const { operatorClick } = useSecurityOperations({
    record: securityDetail,
    dict: ehrDict,
    isInner: true,
  })
  const basicProps = {
    labelCol: { flex: '130px' },
  }

  const handleSave = async () => {
    try {
      const values = await form?.validateFields()
      const data = {
        ...values,
        id,
        accidentDurationInCompany: values.accidentDurationInCompany
          ? values.accidentDurationInCompany?.replace('年', '')
          : undefined,
        followPeriod: values.followPeriod ? values.followPeriod.replace('天', '') : undefined,
        timeAccident: values.timeAccident ? moment(values.timeAccident).valueOf() : undefined,
        claimPaidDate: values.claimPaidDate ? moment(values.claimPaidDate).valueOf() : undefined,
        paymentDate: values.paymentDate ? moment(values.paymentDate).valueOf() : undefined,
        withdrawDate: values.withdrawDate ? moment(values.withdrawDate).valueOf() : undefined,
        completeClaimMaterialPostDate: values.completeClaimMaterialPostDate
          ? moment(values.completeClaimMaterialPostDate).valueOf()
          : undefined,
        suspendDate: values.suspendDate ? moment(values.suspendDate).valueOf() : undefined,
        startRecoverDate: values.startRecoverDate
          ? moment(values.startRecoverDate).valueOf()
          : undefined,
        rewardPunishDate: values.rewardPunishDate
          ? moment(values.rewardPunishDate).valueOf()
          : undefined,
        attachment: values.attachment
          ? JSON.stringify(map(values.attachment, (item) => pick(item, ['uuid', 'name'])))
          : undefined,
      }
      setCommitLoading(true)
      await editDetail(data)
      message.success('修改成功')
      navigate(-1)
    } catch (error) {
      scrollToFirstErrorField({
        errorInfo: error,
        generateFormItemClassFn: generateFormItemClass,
      })
    }
    setCommitLoading(false)
  }

  const handleCancel = () => {
    navigate(-1)
  }

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      if (changedValues[key] && changedValues[key].length > 0 && !hasAttachment) {
        setHasAttachment(true)
      } else if ((!changedValues[key] || changedValues[key].length <= 0) && hasAttachment) {
        setHasAttachment(false)
      }
    }
  }

  useEffect(() => {
    setLoading(true)

    setFooter(
      <Space>
        <Button style={{ marginRight: 20 }} onClick={handleCancel}>
          取消
        </Button>
        <Button type="primary" onClick={handleSave} loading={commitLoading}>
          提交
        </Button>
      </Space>,
    )
    getDetailById({ id })
      .then((res) => {
        const { data } = res
        if (data) {
          const attachment = data.attachment ? JSON.parse(data.attachment) : undefined
          form.setFieldsValue({
            ...pickBy(data, (v) => v !== ''),
            timeEntry: data.timeEntry ? moment(data.timeEntry).format('YYYY-MM-DD') : '--',
            timeLeave: data.timeLeave ? moment(data.timeLeave).format('YYYY-MM-DD') : '--',
            userNameUpdate: data.userNameUpdate
              ? `${data.userNameUpdate}${data.userIdUpdate !== '00000' ? `(${data.userIdUpdate})` : ''
              }`
              : '--',
            timeCreate: data.timeCreate ? moment(data.timeCreate).format('YYYY-MM-DD') : '--',
            timeUpdate: data.timeUpdate ? moment(data.timeUpdate).format('YYYY-MM-DD') : '--',
            followPeriod: data.followPeriod ? `${data.followPeriod}天` : '--',
            timeAccident: data.timeAccident
              ? moment(data.timeAccident).format('YYYY-MM-DD')
              : undefined,
            claimPaidDate: data.claimPaidDate
              ? moment(data.claimPaidDate).format('YYYY-MM-DD')
              : undefined,
            paymentDate: data.paymentDate
              ? moment(data.paymentDate).format('YYYY-MM-DD')
              : undefined,
            withdrawDate: data.withdrawDate
              ? moment(data.withdrawDate).format('YYYY-MM-DD')
              : undefined,
            completeClaimMaterialPostDate: data.completeClaimMaterialPostDate
              ? moment(data.completeClaimMaterialPostDate).format('YYYY-MM-DD')
              : undefined,
            closeDate: data.closeDate ? moment(data.closeDate).format('YYYY-MM-DD') : undefined,
            suspendDate: data.suspendDate
              ? moment(data.suspendDate).format('YYYY-MM-DD')
              : undefined,
            startRecoverDate: data.startRecoverDate
              ? moment(data.startRecoverDate).format('YYYY-MM-DD')
              : undefined,
            rewardPunishDate: data.rewardPunishDate
              ? moment(data.rewardPunishDate).format('YYYY-MM-DD')
              : undefined,
            userIdResponsibleManager: data.userIdResponsibleManager
              ? `${data.userIdResponsibleManagerName}(${data.userIdResponsibleManager})`
              : undefined,
            userIdIncidentResponsibleSupervisor: data.userIdIncidentResponsibleSupervisor
              ? `${data.userIdIncidentResponsibleSupervisorName}(${data.userIdIncidentResponsibleSupervisor})`
              : undefined,
            attachment,
          })
          const title = data?.userName && data?.userNum ? `${data?.userName}(${data?.userNum})` : ''
          const titleElement = (
            <Space align="center">
              <span>{title}</span>
              {data?.process && (
                <Tag color={SECURITY_EVENT_PROCESS_COLOR[data.process]}>
                  {SECURITY_EVENT_PROCESS_NAME[data.process]}
                </Tag>
              )}
            </Space>
          )
          if (title) {
            setTitle(titleElement)
          }
          setSecurityDetail(data)
          setHasAttachment(attachment?.length > 0)
        }
      })
      .finally(() => {
        setLoading(false)
      })

    return () => {
      setFooter(null)
    }
  }, [])

  useEffect(() => {
    if (dhrDict) {
      form.setState({ dhrDict })
    }
    if (ehrDict) {
      form.setState({ ehrDict })
    }
  }, [ehrDict, dhrDict])

  useEffect(() => {
    // TODO:设置权限
    setExtraElement(
      <>
        <Button
          type="primary"
          ghost
          disabled={!isInProgress(securityDetail)}
          onClick={() => operatorClick('compensation_apply')}
        >
          赔偿额度申请
        </Button>
        <Button
          type="primary"
          ghost
          disabled={!isInProgress(securityDetail)}
          onClick={() => operatorClick('payment_apply')}
        >
          三方付款申请
        </Button>
        <Dropdown
          menu={{
            items: [
              {
                label: (
                  <Button
                    type="link"
                    style={{ padding: 0, height: 'auto' }}
                    disabled={!isInProgress(securityDetail)}
                  >
                    三方借款申请
                  </Button>
                ),
                key: 'loan_apply',
                disabled: !isInProgress(securityDetail),
              },
              {
                label: (
                  <Button
                    type="link"
                    style={{ padding: 0, height: 'auto' }}
                    disabled={!isInProgress(securityDetail)}
                  >
                    回款审批
                  </Button>
                ),
                key: 'repayment_approval',
                disabled: !isInProgress(securityDetail),
              },
              {
                label: (
                  <Button
                    type="link"
                    style={{ padding: 0, height: 'auto' }}
                    disabled={!isInProgress(securityDetail)}
                  >
                    不追偿申请
                  </Button>
                ),
                key: 'no_repayment_apply',
                disabled: !isInProgress(securityDetail),
              },
              {
                label: (
                  <Button
                    type="link"
                    style={{ padding: 0, height: 'auto' }}
                    disabled={!canUpgradeToDispute(securityDetail)}
                  >
                    升级至安全争议
                  </Button>
                ),
                key: 'upgrade_to_dispute',
                disabled: !canUpgradeToDispute(securityDetail),
              },
              {
                label: (
                  <Button
                    type="link"
                    style={{ padding: 0, height: 'auto' }}
                    disabled={!isInProgress(securityDetail)}
                  >
                    结案
                  </Button>
                ),
                key: 'close',
                disabled: !isInProgress(securityDetail),
              },
              {
                label: (
                  <Button
                    type="link"
                    style={{ padding: 0, height: 'auto' }}
                    disabled={!isInProgress(securityDetail)}
                  >
                    撤案
                  </Button>
                ),
                key: 'withdraw',
                disabled: !isInProgress(securityDetail),
              },
              {
                label: (
                  <Button
                    type="link"
                    style={{ padding: 0, height: 'auto' }}
                    disabled={!isWithdrawn(securityDetail)}
                  >
                    取消撤案
                  </Button>
                ),
                key: 'cancel_withdraw',
                disabled: !isWithdrawn(securityDetail),
              },
            ],
            onClick: ({ key }) => operatorClick(key),
          }}
        >
          <Button type="primary" ghost>
            <Space>
              更多
              <DownOutlined />
            </Space>
          </Button>
        </Dropdown>
      </>,
    )

    return () => {
      setExtraElement(null)
    }
  }, [securityDetail, operatorClick])

  const schema = useMemo(() => getSchema({ ucDict }), [ucDict])

  return loading ? (
    <PageLoading style={{ minHeight: 'calc(100vh - 300px)', backgroundColor: '#fff' }} />
  ) : (
    <div>
      <SchemaForm
        schema={schema}
        layout="horizontal"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 10 }}
        form={form}
        initialValues={{
          isViolation: false,
        }}
        className={style.scheduleForm}
        onValuesChange={onValuesChange}
      >
        <Col span="24">
          <div className={style.employeeDetail}>
            <Row>
              <Col span="24">
                <Row gutter={0} align="top">
                  <Col span={6}>
                    <Item {...generateFormItemConfig('contact')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('positionName')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('positionType')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('departmentLevelFourth')} {...basicProps} />
                  </Col>
                </Row>
              </Col>
              <Col span="24">
                <Row gutter={0} align="top">
                  <Col span={6}>
                    <Item {...generateFormItemConfig('timeEntry')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('timeLeave')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('timeCreate')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('userNameUpdate')} {...basicProps} />
                  </Col>
                </Row>
              </Col>
              <Col span="24" className={style.lastCol}>
                <Row gutter={0} align="top">
                  <Col span={6}>
                    <Item {...generateFormItemConfig('timeUpdate')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('oaNo')} {...basicProps} />
                  </Col>
                  <Col span={6}>
                    <Item {...generateFormItemConfig('id')} {...basicProps} />
                  </Col>
                </Row>
              </Col>
            </Row>
          </div>
        </Col>
        <Row>
          <Col span="24">
            <div className={style.anchorWrapper}>
              <DhrAnchor
                options={[
                  { title: '案件信息', message: '' },
                  { title: '申报保险信息', message: '' },
                  { title: '进度管控', message: '' },
                  { title: '费用管理' },
                  { title: '借支管理' },
                  { title: '奖惩信息' },
                  { title: '单据信息' },
                  { title: '附件' },
                ]}
              >
                <Card title="案件信息" size="default" key="information">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentYearMonth')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('timeAccident')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentDurationInCompany')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentDurationCategory')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentArea')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('eventLocation')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('dateCategory')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('eventCategory')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isViolation')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('violationType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('weather')} />
                      </Col>
                    </Row>
                    <Item
                      {...generateFormItemConfig('accidentContent')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('userIdResponsibleManager')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('userIdIncidentResponsibleSupervisor')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentNature')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isThirdPartyInvolved')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyName')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyAge')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyContact')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('responsibilityAllocation')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentScenario')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('injuredPersonnelCondition')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('staffInjuryType')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('thirdPartyInjuryType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('eventCoefficient')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('eventCoefficientChangeReason')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('deliveryOrderNum')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('followCycleCategory')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentContractSubject')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentContractType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('currentContractSubjectName')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('currentContractType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentDepartmentName')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('accidentPositionName')} />
                      </Col>
                    </Row>
                    <Item
                      {...generateFormItemConfig('remark')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                    <Item
                      {...generateFormItemConfig('remarkOther')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                    <Item
                      {...generateFormItemConfig('remarkOtherTwo')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                  </>
                </Card>

                <Card title="申报保险信息" size="default" key="InsuranceInformation">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('reportUnit')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('insureType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isApplyWorkInjuryRecognition')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('completeClaimMaterialPostDate')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('claimPaidDate')} />
                      </Col>
                    </Row>
                  </>
                </Card>

                <Card title="进度管控" size="default" key="schedule">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('followStatus')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isElectricMotorcycleCase')} />
                      </Col>
                    </Row>
                    <Item
                      {...generateFormItemConfig('processRemark')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('followPeriod')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('followPeriodCategory')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('unclosedReason')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('paymentDate')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('closeDate')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('withdrawDate')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('withdrawReason')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isWorkInjury')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isApplyLaborAbilityIdentification')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('disabilityLevel')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isApplyWorkInjuryCompensation')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isDisputed')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('disputeEventId')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('originalEventId')} />
                      </Col>
                    </Row>
                    <Row>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isSuspend')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('suspendDate')} />
                      </Col>
                    </Row>
                    <Item
                      {...generateFormItemConfig('suspendReason')}
                      wrapperCol={{ span: 17 }}
                      labelCol={{ span: 4 }}
                    />
                  </>
                </Card>

                <Card title="费用管理" size="default" key="costManagement">
                  <div className={style.subTitle}>赔偿信息</div>
                  <FormItemGrid fillEmpty colCount={2}>
                    <Item {...generateFormItemConfig('mediationRequestedAmount')} />
                    <Item {...generateFormItemConfig('statutoryCompensationAmount')} />
                    <Item {...generateFormItemConfig('compensationType')} />
                    <Item {...generateFormItemConfig('totalMedicalExpense')} />
                    <Item {...generateFormItemConfig('insuranceEstimatedAmount')} />
                  </FormItemGrid>
                  <Divider />
                  <div className={style.subTitle}>员工承担费用</div>
                  <FormItemGrid fillEmpty colCount={2}>
                    <Item {...generateFormItemConfig('staffEstimatedPayableAmount')} />
                    <Item {...generateFormItemConfig('employeePayableAmount')} />
                    <Item {...generateFormItemConfig('employeePaidAmount')} />
                    <Item {...generateFormItemConfig('employeeRefundableAmount')} />
                    <Item {...generateFormItemConfig('employeeActualRefundAmount')} />
                    <Item {...generateFormItemConfig('employeeActualPayableAmount')} />
                  </FormItemGrid>
                  <Divider />
                  <div className={style.subTitle}>公司承担费用</div>
                  <FormItemGrid fillEmpty colCount={2}>
                    <Item {...generateFormItemConfig('companyEstimatedPayableAmount')} />
                    <Item {...generateFormItemConfig('companyPayableAmount')} />
                    <Item {...generateFormItemConfig('companyActualTotalPaidAmount')} />
                    <Item {...generateFormItemConfig('companyCurrentPaidAmount')} />
                    <Item {...generateFormItemConfig('companyAdvancedAmount')} />
                    <Item {...generateFormItemConfig('companyActualCompensationAmount')} />
                  </FormItemGrid>
                  <Divider />
                  <div className={style.subTitle}>结算与第三方</div>
                  <FormItemGrid fillEmpty colCount={2}>
                    <Item {...generateFormItemConfig('eventCloseAmount')} />
                    <Item {...generateFormItemConfig('thirdPartyLiableAmount')} />
                    <Item {...generateFormItemConfig('insuranceActualCompensationAmount')} />
                    <Item {...generateFormItemConfig('workInjuryInsuranceClaimAmount')} />
                    <Item {...generateFormItemConfig('actualTotalExpenditure')} />
                    <Item {...generateFormItemConfig('actualTotalIncome')} />
                    <Item {...generateFormItemConfig('recoverableAmount')} />
                    <Item {...generateFormItemConfig('recoupedAmount')} />
                    <Item {...generateFormItemConfig('waivedRecoveryAmount')} />
                  </FormItemGrid>
                  <Item
                    {...generateFormItemConfig('nonRecoveryRequestDescription')}
                    wrapperCol={{ span: 17 }}
                    labelCol={{ span: 4 }}
                  />
                </Card>
                <Card title="借支管理" size="default" key="borrowManagement">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('isBorrow')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top" justify="center" style={{ marginBottom: 32 }}>
                      <Col span={18}>
                        <AdvanceManagementTable />
                      </Col>
                    </Row>
                    <Divider />
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('currentDebt')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top" justify="center" style={{ marginBottom: 32 }}>
                      <Col span={18}>
                        <RepaymentManagementTable />
                      </Col>
                    </Row>
                  </>
                </Card>

                <Card title="奖惩信息" size="default" key="rewardInformation">
                  <>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <>
                          <Item {...generateFormItemConfig('isStaffPenalty')} />
                        </>
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('penaltyType')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('noPenaltyReasonDetails')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('rewardPunishDate')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <Item {...generateFormItemConfig('rewardPunishType')} />
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('rewardPunishCategory')} />
                      </Col>
                    </Row>
                    <Row gutter={0} align="top">
                      <Col span={12}>
                        <>
                          <Item {...generateFormItemConfig('rewardPunishProgress')} />
                          <Item {...generateFormItemConfig('noPenaltyReason')} />
                        </>
                      </Col>
                      <Col span={12}>
                        <Item {...generateFormItemConfig('notImplementedRewardPunishReason')} />
                      </Col>
                    </Row>
                  </>
                </Card>
                <Card title="单据信息" size="default" key="documentInformation">
                  <InvoiceTable
                    pageName="security"
                    data={form.getFieldValue('documents')}
                    userName={form.getFieldValue('userName')}
                    userNum={form.getFieldValue('userNum')}
                  />
                </Card>
                <Card
                  title="附件"
                  size="default"
                  key="appendix"
                  extra={
                    <BusinessPermission code={SECURITY.BATCH_DOWNLOAD}>
                      <Button
                        type="link"
                        disabled={!hasAttachment}
                        onClick={() => {
                          const attachment = form.getFieldValue('attachment')
                          const fileName = `${securityDetail?.userName}_${securityDetail?.id
                            }_${moment().valueOf()}`
                          batchPackDownload(attachment, fileName)
                        }}
                      >
                        打包下载
                      </Button>
                    </BusinessPermission>
                  }
                >
                  <Item field="attachment" />
                </Card>
              </DhrAnchor>
            </div>
          </Col>
        </Row>
      </SchemaForm>
      <Footer>{footer}</Footer>
    </div>
  )
}

export default Schedule
