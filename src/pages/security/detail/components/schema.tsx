import React from 'react'
import { Observer, SchemaType } from '@amazebird/antd-schema-form'
import {
  CLOSE_CASE_HIDE_VALUE,
  EVENT_CLASSIFICATION,
  FOLLOW_STATUS_NO_FOLLOW_CODE,
  SECURITY_EVENT_PROCESS,
  UC_DICT_KEY,
} from '@/constants'
import { map, filter, isNil, isObject, findIndex, keys } from 'lodash-es'
import { Typography } from 'antd'
import moment from 'moment'
import { calcYear } from './utils'
import { attachmentField } from '../../utils'

type EllipsisTextProps = {
  value?: any
  format?: string
  options?: any[]
  ellipsis?: boolean
  [key: string]: any
}

const { Text } = Typography

const EllipsisText = (props: EllipsisTextProps) => {
  const ellipsis = isNil(props?.ellipsis) ? true : props.ellipsis
  let text = props.value
  if (props?.format && text) {
    text = moment(props.value).format(props?.format)
  } else if (props?.options && props.options.length > 0) {
    text = map(
      filter(props?.options, (item) => item.value === props.value),
      (item) => item.label,
    ).join('；')
  } else if (isObject(text) && 'label' in text) {
    text = text.label
  }
  return <Text ellipsis={ellipsis ? { tooltip: text || '--' } : false}>{text || '--'}</Text>
}

const textField = {
  mode: 'detail',
  renderItem: () => (props) => {
    return <EllipsisText {...props} />
  },
}

export const detailNumberField = {
  component: 'InputNumber',
  mode: 'detail',
  props: {
    addonAfter: '元',
    precision: 2,
  },
}

export const getSchema = (extra?: Record<string, any>) => {
  const schema: SchemaType = {
    contact: {
      component: 'Input',
      label: '联系方式',
      ...textField,
    },
    positionName: {
      component: 'Input',
      label: '岗位',
      ...textField,
    },
    positionType: {
      component: 'DictSelect',
      label: '岗位类型划分',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_PostType?.list || []
          }
          return []
        },
      }),
      mode: 'detail',
    },
    departmentLevelFourth: {
      component: 'Input',
      label: '四级部门',
      ...textField,
    },
    timeEntry: {
      component: 'Input',
      label: '入职日期',
      ...textField,
    },
    timeLeave: {
      component: 'Input',
      label: '离职日期',
      ...textField,
    },
    timeCreate: {
      component: 'Input',
      label: '事件生成日期',
      ...textField,
    },
    userNameUpdate: {
      component: 'Input',
      label: '最后操作人',
      ...textField,
    },
    timeUpdate: {
      component: 'Input',
      label: '最后操作时间',
      ...textField,
    },
    oaNo: {
      component: 'Input',
      label: '危机管理报告单号',
      ...textField,
    },
    id: {
      component: 'Input',
      label: '事件ID',
      ...textField,
    },
    accidentContent: {
      component: 'Input.TextArea',
      label: '案件内容',
      required: true,
      props: {
        showCount: false,
      },
    },
    accidentNature: {
      component: 'DictSelect',
      label: '事故性质',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].THE_ACCIDENT_NATURE?.list || []
          }
          return []
        },
      }),
      required: true,
      props: {
        mode: 'multiple',
      },
    },
    accidentScenario: {
      component: 'DictSelect',
      label: '案发场景',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_OCCURSENCE?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    claimPaidDate: {
      component: 'DatePicker',
      label: '理赔到账日期',
    },
    eventCloseAmount: {
      label: '结案金额',
      ...detailNumberField,
    },
    closeDate: {
      component: 'DatePicker',
      label: '结案日期',
      ...textField,
    },
    withdrawDate: {
      label: '撤案日期',
      component: 'DatePicker',
      mode: 'detail',
    },
    followStatus: {
      label: '跟进状态',
      component: 'DictSelect',
      options: Observer({
        watch: ['#ehrDict', 'process'],
        action: ([dict, process]) => {
          if (process === SECURITY_EVENT_PROCESS.inProgress) {
            return dict?.BM_FOLLOW_STATUS?.list.filter(
              (item) => item.code !== FOLLOW_STATUS_NO_FOLLOW_CODE,
            )
          }
          // 只有无需跟进的选项
          const nonList = dict?.BM_FOLLOW_STATUS?.list.filter(
            (item) => item.code === FOLLOW_STATUS_NO_FOLLOW_CODE,
          )
          return nonList || []
        },
      }),
    },
    isElectricMotorcycleCase: {
      label: '是否电摩案件',
      component: 'Select',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    companyRepayAmount: {
      component: 'InputNumber',
      label: '公司还款金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    completeClaimMaterialPostDate: {
      component: 'DatePicker',
      label: '保司收到完整理赔材料日期',
    },
    currentDebt: {
      component: 'InputNumber',
      mode: 'detail',
      label: '员工未还金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    deliveryOrderNum: {
      component: 'Input',
      label: '配送订单号',
      props: {
        maxLength: 30,
      },
    },
    disabilityLevel: {
      component: 'DictSelect',
      label: '伤残等级',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_INJURE_DEGREE?.list || []
          }
          return []
        },
      }),
    },
    staffRepayAmount: {
      component: 'InputNumber',
      label: '员工还款金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    staffTotalDebt: {
      component: 'InputNumber',
      label: '员工总欠款金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    mediationRequestedAmount: {
      label: '案件申请调解金额',
      ...detailNumberField,
    },
    statutoryCompensationAmount: {
      label: '法定赔偿金额',
      ...detailNumberField,
    },
    compensationType: {
      label: '赔付类型',
      ...textField,
    },
    totalMedicalExpense: {
      label: '医疗费总额',
      ...detailNumberField,
    },
    insuranceEstimatedAmount: {
      label: '保险预估金额',
      ...detailNumberField,
    },
    staffEstimatedPayableAmount: {
      label: '员工预估承担金额',
      ...detailNumberField,
    },
    employeePayableAmount: {
      label: '员工应承担金额',
      ...detailNumberField,
    },
    employeePaidAmount: {
      label: '员工已支付金额',
      ...detailNumberField,
    },
    employeeRefundableAmount: {
      label: '应退还员工费用',
      ...detailNumberField,
    },
    employeeActualRefundAmount: {
      label: '实际退还员工费用',
      ...detailNumberField,
    },
    employeeActualPayableAmount: {
      label: '员工实际承担金额',
      ...detailNumberField,
    },
    companyEstimatedPayableAmount: {
      label: '公司预估承担金额',
      ...detailNumberField,
    },
    companyPayableAmount: {
      label: '公司应承担金额',
      ...detailNumberField,
    },
    companyAdvancedAmount: {
      label: '公司已垫付金额',
      ...detailNumberField,
    },
    companyActualTotalPaidAmount: {
      label: '公司实际支付总额',
      ...detailNumberField,
    },
    companyCurrentPaidAmount: {
      label: '公司本次支付金额',
      ...detailNumberField,
    },
    companyActualCompensationAmount: {
      label: '公司实际承担金额',
      ...detailNumberField,
    },
    thirdPartyLiableAmount: {
      label: '第三方承担金额',
      ...detailNumberField,
    },
    insuranceActualCompensationAmount: {
      label: '保险实际赔付金额',
      ...detailNumberField,
    },
    workInjuryInsuranceClaimAmount: {
      label: '工伤保险理赔金额',
      ...detailNumberField,
    },
    actualTotalExpenditure: {
      label: '实际总支出',
      ...detailNumberField,
    },
    actualTotalIncome: {
      label: '实际总收入',
      ...detailNumberField,
    },
    recoverableAmount: {
      label: '可追偿金额',
      ...detailNumberField,
    },
    recoupedAmount: {
      label: '已追回金额',
      ...detailNumberField,
    },
    waivedRecoveryAmount: {
      label: '放弃追偿金额',
      ...detailNumberField,
    },
    eventCoefficientChangeReason: {
      component: 'Input',
      label: '案件系数变更原因',
      props: {
        maxLength: 100,
      },
    },
    eventCoefficient: {
      component: 'DictSelect',
      label: '案件系数',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_AJXS?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    injuredPersonnelCondition: {
      component: 'DictSelect',
      label: '受伤人员情况',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_INJURYSTATUS?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    insuranceRepayAmount: {
      component: 'InputNumber',
      label: '保险还款金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    insureType: {
      component: 'DictSelect',
      label: '申报险种',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BX_SFXZ?.list || []
          }
          return []
        },
      }),
      required: true,
      props: {
        mode: 'multiple',
      },
    },
    isApplyLaborAbilityIdentification: {
      component: 'Select',
      label: '是否申请劳动能力鉴定',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isApplyWorkInjuryCompensation: {
      component: 'Select',
      label: '是否申请工伤待遇赔偿',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isDisputed: {
      label: '是否争议',
      component: 'Select',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      mode: 'detail',
    },
    disputeEventId: {
      label: '结案后争议ID',
      renderItem: () => (props) => {
        return props.value ? (
          <a href={`/dhr-employee-relations-webapp/security/detail?id=${props.value}`}>
            {props.value}
          </a>
        ) : (
          <span>--</span>
        )
      },
    },
    originalEventId: {
      label: '关联安全事件ID',
      renderItem: () => (props) => {
        return props.value ? (
          <a href={`/dhr-employee-relations-webapp/security/detail?id=${props.value}`}>
            {props.value}
          </a>
        ) : (
          <span>--</span>
        )
      },
    },
    isApplyWorkInjuryRecognition: {
      component: 'Select',
      label: '是否申请认定工伤',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      required: true,
    },
    isBorrow: {
      component: 'Select',
      label: '是否借支',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      ...textField,
    },
    isStaffPenalty: {
      component: 'Select',
      label: '出险员工是否处罚',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isSuspend: {
      component: 'Select',
      label: '是否挂起',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    isThirdPartyInvolved: {
      component: 'Select',
      label: '是否涉及第三方',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      required: true,
    },
    isWorkInjury: {
      component: 'Select',
      label: '是否认定为工伤',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
    notImplementedRewardPunishReason: {
      component: 'DictSelect',
      label: '未实行奖惩原因',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_250?.list || []
          }
          return []
        },
      }),
    },
    penaltyType: {
      component: 'DictSelect',
      label: '处罚类型',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].TYPE_OF_PENALTY?.list || []
          }
          return []
        },
      }),
    },
    processRemark: {
      component: 'Input.TextArea',
      label: '进度说明',
      props: {
        maxLength: 1000,
      },
    },
    recoverAmount: {
      component: 'InputNumber',
      label: '返还金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    recoverMethod: {
      component: 'Input',
      label: '返还方式',
      props: {
        maxLength: 100,
      },
    },
    recoveredAmount: {
      component: 'InputNumber',
      label: '已返还金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    nonRecoveryRequestDescription: {
      component: 'Input',
      label: '申请不追偿说明',
      ...textField,
    },
    remark: {
      component: 'Input.TextArea',
      label: '案件备注',
      props: {
        maxLength: 1000,
      },
    },
    remarkOther: {
      component: 'Input.TextArea',
      label: '案件备注1',
      props: {
        maxLength: 1000,
      },
    },
    remarkOtherTwo: {
      component: 'Input.TextArea',
      label: '案件备注2',
      props: {
        maxLength: 1000,
      },
    },
    repayProgress: {
      component: 'DictSelect',
      label: '还款进度',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_REPAYSCHE?.list || []
          }
          return []
        },
      }),
    },
    reportUnit: {
      component: 'DictSelect',
      label: '申报单位',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_REPORT_CO?.list || []
          }
          return []
        },
      }),
      required: true,
      props: {
        mode: 'multiple',
      },
    },
    responsibilityAllocation: {
      component: 'DictSelect',
      label: '责任划分',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_RESPONSIBILITY?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    rewardPunishCategory: {
      component: 'DictSelect',
      label: '奖惩类别',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].REWARD_PUNISHMENT_CATEGORIES?.list || []
          }
          return []
        },
      }),
    },
    rewardPunishDate: {
      component: 'DatePicker',
      label: '奖惩日期',
    },
    rewardPunishProgress: {
      component: 'DictSelect',
      label: '奖惩进度',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_249?.list || []
          }
          return []
        },
      }),
    },
    rewardPunishType: {
      component: 'DictSelect',
      label: '奖惩类型',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_56?.list || []
          }
          return []
        },
      }),
    },
    staffInjuryType: {
      component: 'DictSelect',
      label: '员工受伤类型',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_WOUNDTYPE?.list || []
          }
          return []
        },
      }),
    },
    startRecoverDate: {
      component: 'DatePicker',
      label: '开始返还日期',
    },
    suspendDate: {
      component: 'DatePicker',
      label: '挂起日期',
    },
    suspendReason: {
      component: 'Input.TextArea',
      label: '挂起原因',
      props: {
        maxLength: 1000,
      },
    },
    thirdPartyAge: {
      component: 'Input',
      label: '第三方年龄',
      props: {
        maxLength: 30,
      },
      mode: 'detail',
    },
    thirdPartyContact: {
      component: 'Input',
      label: '第三方联系方式',
      pattern: /^[0-9]*$/,
      props: {
        maxLength: 11,
      },
    },
    thirdPartyInjuryType: {
      component: 'DictSelect',
      label: '第三方受伤类型',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_WOUNDTYPE?.list || []
          }
          return []
        },
      }),
    },
    thirdPartyName: {
      component: 'Input',
      label: '第三方姓名',
      props: {
        maxLength: 30,
      },
    },
    totalBorrowAmount: {
      component: 'InputNumber',
      label: '总借支金额',
      max: 10000000,
      min: 0,
      props: {
        addonAfter: '元',
        precision: 2,
      },
    },
    unclosedReason: {
      component: 'DictSelect',
      label: '未结案原因',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            const filterList = v?.[0].BM_CLOSE_CASE?.list.map((item) => {
              if (CLOSE_CASE_HIDE_VALUE.indexOf(item.code) !== -1) {
                return {
                  ...item,
                  isEnabled: false,
                }
              }
              return item
            })
            return filterList || []
          }
          return []
        },
      }),
    },
    paymentDate: {
      label: '支付日期',
      component: 'DatePicker',
      mode: 'detail',
    },
    userIdResponsibleManager: {
      component: 'Input.Text',
      label: '责任店长',
      props: {
        showSearch: true,
        onlySearch: true,
      },
    },
    userIdIncidentResponsibleSupervisor: {
      label: '案发责任主管',
      component: 'Input.Text',
      props: {
        showSearch: true,
        onlySearch: true,
      },
    },
    weather: {
      component: 'DictSelect',
      label: '天气',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].THE_WEATHER?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    withdrawReason: {
      component: 'DictSelect',
      label: '撤案原因',
      mode: 'detail',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_CANCELREASON?.list || []
          }
          return []
        },
      }),
    },
    accidentYearMonth: {
      component: 'Input',
      label: '案发年月',
      ...textField,
    },
    timeAccident: {
      component: 'DatePicker',
      label: '事故日期',
      props: {
        format: 'YYYY-MM-DD',
      },
      mode: 'detail',
    },
    accidentDurationInCompany: {
      component: 'Input',
      label: '案发在司时长',
      ...textField,
      value: Observer({
        watch: ['timeAccident', 'timeEntry'],
        action: async (value) => {
          const timeAccidentValue = moment(value[0]).valueOf()
          const timeEntryMomentValue = moment(value[1]).valueOf()
          return calcYear(timeAccidentValue, timeEntryMomentValue)
        },
      }),
    },
    accidentDurationCategory: {
      component: 'Input',
      label: '在司时长分类',
      ...textField,
      value: Observer({
        watch: 'accidentDurationInCompany',
        action: async (accidentDurationInCompany) => {
          if (isNil(accidentDurationInCompany)) {
            return '--'
          }
          const value = Number(accidentDurationInCompany.replace('年', ''))
          if (value >= 0 && value < 0.083) {
            return '1个月以内'
          }
          if (value >= 0.083 && value < 0.25) {
            return '1-3个月'
          }
          if (value >= 0.25 && value < 0.5) {
            return '3-6个月'
          }
          if (value >= 0.5 && value < 1) {
            return '6个月-1年'
          }
          if (value >= 1 && value < 2) {
            return '1-2年'
          }
          if (value >= 2) {
            return '2年以上'
          }
          return '--'
        },
      }),
    },
    accidentArea: {
      component: 'DictSelect',
      label: '案发区域',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_BZGSQY?.list || []
          }
          return []
        },
      }),
      mode: 'detail',
    },
    eventLocation: {
      component: 'Input',
      label: '事件发生地点',
      ...textField,
    },
    dateCategory: {
      component: 'DictSelect',
      label: '时间分类',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].TIME_CLASSIFICATION?.list || []
          }
          return []
        },
      }),
      required: true,
      mode: 'detail',
    },
    eventCategory: {
      component: 'DictSelect',
      label: '事件分类',
      options: Observer({
        watch: ['#dhrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].EVENT_CLASSIFICATION?.list || []
          }
          return []
        },
      }),
      required: true,
    },
    isViolation: {
      label: '是否违章',
      component: 'Select',
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      rules: Observer({
        watch: ['eventCategory'],
        action: ([value]) => {
          if (value === EVENT_CLASSIFICATION.trafficAccident) {
            return [{ required: true, message: '请选择是否违章' }]
          }
          return []
        },
      }),
    },
    violationType: {
      label: '违章类型',
      component: 'DictSelect',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_VIOLATION_TYPE?.list || []
          }
          return []
        },
      }),
      rules: Observer({
        watch: ['eventCategory', 'isViolation'],
        action: ([eventCategory, isViolation]) => {
          if (eventCategory === EVENT_CLASSIFICATION.trafficAccident && isViolation) {
            return [{ required: true, message: '请选择违章类型' }]
          }
          return []
        },
      }),
    },
    followCycleCategory: {
      component: 'Input',
      label: '跟进周期分类',
      mode: 'detail',
      value: Observer({
        watch: ['eventCoefficient', 'followPeriod'],
        action: async (value) => {
          const followPeriod = Number(value[1].replace('天', ''))
          if ((value[0] === '01' || value[0] === '02') && followPeriod > 90) {
            return '超过时限要求'
          }
          if (value[0] === '03' && followPeriod > 130) {
            return '超过时限要求'
          }
          if (value[0] === '04' && followPeriod > 180) {
            return '超过时限要求'
          }
          if (!value[0]) {
            return '--'
          }
          return '在时限要求内'
        },
      }),
    },
    accidentContractSubject: {
      component: 'DictSelect',
      label: '案发合同主体',
      mode: 'detail',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_HTZT?.list || []
          }
          return []
        },
      }),
    },
    // accidentContractSubjectName: {
    //   component: 'Text',
    //   label: '案发合同主体',
    //   mode: 'detail',
    // },
    accidentContractType: {
      component: 'DictSelect',
      label: '案发合同主体类型',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_VAA?.list || []
          }
          return []
        },
      }),
      mode: 'detail',
    },
    currentContractSubjectName: {
      component: 'Text',
      label: '当前合同主体',
      mode: 'detail',
    },
    currentContractType: {
      component: 'DictSelect',
      label: '当前合同主体类型',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_VAA?.list || []
          }
          return []
        },
      }),
      mode: 'detail',
    },
    accidentDepartmentName: {
      component: 'Input',
      label: '案发部门名称',
      ...textField,
    },
    accidentPositionName: {
      component: 'Input',
      label: '案发岗位名称',
      ...textField,
    },
    followPeriod: {
      component: 'Input',
      label: '跟进时长',
      ...textField,
      value: Observer({
        watch: ['timeAccident', 'process', 'closeDate'],
        action: async (value) => {
          const timeAccidentValue = moment(value[0])
          const closeDateValue = moment(value[2])
          if (value[0] && value[1] === '1') {
            return `${moment().diff(timeAccidentValue, 'day')}天`
          }
          if (value[0] && (value[1] === '2' || value[1] === '3') && value[2]) {
            return `${closeDateValue.diff(timeAccidentValue, 'day')}天`
          }
          return '--'
        },
      }),
    },
    followPeriodCategory: {
      component: 'Text',
      label: '跟进时长分类',
      mode: 'detail',
      value: Observer({
        watch: 'followPeriod',
        action: async (followPeriod) => {
          if (isNil(followPeriod)) {
            return '--'
          }
          const value = Number(followPeriod.replace('天', ''))
          // A. 0＜跟进时长≤90时，类型为【0-3个月】；
          // B. 90＜跟进时长≤180时，类型为【3-6个月】；
          // C. 180<跟进时长＜365时，类型为【6-12个月】；
          // D. 365≤跟进时长＜730时，类型为【12-24个月】；
          // E. 跟进时长≥730时，类型为【24个月以上】。
          if (value > 0 && value <= 90) {
            return '0-3个月'
          }
          if (value > 90 && value <= 180) {
            return '3-6个月'
          }
          if (value > 180 && value < 365) {
            return '6-12个月'
          }
          if (value >= 365 && value < 730) {
            return '12-24个月'
          }
          if (value >= 730) {
            return '24个月以上'
          }
          return '--'
        },
      }),
    },
    noPenaltyReasonDetails: {
      component: 'Input',
      label: '不处罚原因详情',
      ...textField,
    },
    noPenaltyReason: {
      component: 'DictSelect',
      label: '不处罚原因',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_NoPunishReason?.list || []
          }
          return []
        },
      }),
      mode: 'detail',
    },
    attachment: {
      ...attachmentField,
      label: '',
    },
  }
  // 字段权限处理
  const authFields = map(
    extra?.ucDict?.[UC_DICT_KEY.SECURITY_AUTH_FIELD] || [],
    (subItem) => subItem.code,
  )
  // 人员组织选择器 展示组织树结构权限
  const hasDeptAuth =
    extra?.ucDict &&
    extra?.ucDict?.[UC_DICT_KEY.DEPT_AUTH].length > 0 &&
    findIndex(extra?.ucDict?.[UC_DICT_KEY.DEPT_AUTH], (item: any) => item?.code === '1') >= 0
  map(keys(schema), (key: string) => {
    if (!authFields.includes(key) && !schema[key].mode) {
      schema[key].mode = 'detail'
      schema[key].required = false
    }

    // 人员组织选择器控制权限
    if (['userIdResponsibleManager', 'userIdIncidentResponsibleSupervisor'].includes(key)) {
      schema[key].props = {
        ...schema[key].props,
        onlySearch: !hasDeptAuth,
      }
    }
  })
  return schema
}
