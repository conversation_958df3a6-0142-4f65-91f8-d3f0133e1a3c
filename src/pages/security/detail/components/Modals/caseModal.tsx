import React from 'react'
import { Modal, Form, DatePicker, Typography, Select } from 'antd'
import moment from 'moment'

const { Text } = Typography

export enum CaseModalType {
  Close = 'close',
  Withdraw = 'withdraw',
}

interface IProps {
  visible: boolean
  record: any
  onOk?: (values: any) => void
  close?: () => void
  dict: any
  type: CaseModalType
}

const CaseModal: React.FC<IProps> = (props) => {
  const { visible, record, onOk, close, type, dict } = props
  const [form] = Form.useForm()

  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      onOk?.(values)
      close?.()
    } catch (error) {
      console.error('Validation failed:', error)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    close?.()
  }

  // 从今天开始前的30天到今天（包含今天）
  const disabledDate = (current) => {
    if (!current) {
      return false
    }

    const today = moment().endOf('day')
    const thirtyDaysAgo = moment().subtract(30, 'days').startOf('day')

    // 禁用今天之后的日期和30天前之前的日期
    return current.isAfter(today) || current.isBefore(thirtyDaysAgo)
  }

  return (
    <Modal
      title={`${type === CaseModalType.Close ? '结案' : '撤案'}-${record?.userName}（${
        record?.userNum
      }）`}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText="确定"
      cancelText="取消"
      destroyOnClose
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={{
          closeDate: type === CaseModalType.Close ? moment() : undefined,
        }}
      >
        <Form.Item
          label={type === CaseModalType.Close ? '结案日期' : '撤案日期'}
          name="closeDate"
          rules={[
            {
              required: type === CaseModalType.Close,
              message: type === CaseModalType.Close ? '请选择结案日期' : '请选择撤案日期',
            },
          ]}
        >
          {type === CaseModalType.Close ? (
            <DatePicker
              style={{ width: '100%' }}
              placeholder="请选择结案日期"
              disabledDate={disabledDate}
            />
          ) : (
            <Text>{moment().format('YYYY-MM-DD')}</Text>
          )}
        </Form.Item>
        {type === CaseModalType.Withdraw && (
          <Form.Item
            label="撤案原因"
            name="withdrawReason"
            rules={[
              {
                required: type === CaseModalType.Withdraw,
                message: '请选择撤案原因',
              },
            ]}
          >
            <Select
              options={dict?.BM_CANCELREASON?.list || []}
              placeholder="请选择撤案原因"
              allowClear
            />
          </Form.Item>
        )}
      </Form>
    </Modal>
  )
}

export default CaseModal
