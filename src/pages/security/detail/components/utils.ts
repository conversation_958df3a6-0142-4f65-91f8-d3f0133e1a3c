import moment from 'moment'

export const calcYear = (timeStamp1: number, timeStamp2: number) => {
  const moment1 = moment(timeStamp1)
  const moment2 = moment(timeStamp2)
  // 计算它们之间的差值，单位为年
  const yearsDifference = moment1.diff(moment2, 'years', true) // true 表示返回浮点数
  // 保留三位小数
  const yearsDifferenceRounded = parseFloat(yearsDifference.toFixed(3))
  // 拼接年
  return yearsDifferenceRounded >= 0 ? `${yearsDifferenceRounded}年` : '--'
}
