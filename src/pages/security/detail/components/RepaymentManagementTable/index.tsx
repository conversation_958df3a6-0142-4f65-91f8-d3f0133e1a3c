import React from 'react'
import { Table, Typography, Card } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import styles from './index.module.less'

const { Text } = Typography

interface RepaymentRecord {
  key: string
  repaymentDate: string
  repaymentType: string
  repaymentAmount: number
}

interface IProps {
  data?: RepaymentRecord[]
}

const RepaymentManagementTable: React.FC<IProps> = ({ data }) => {
  // 模拟数据
  const dataSource: RepaymentRecord[] = [
    {
      key: '1',
      repaymentDate: '2024-09-21',
      repaymentType: '工资月还',
      repaymentAmount: 5000.0,
    },
    {
      key: '2',
      repaymentDate: '2022-09-01',
      repaymentType: '员工回款',
      repaymentAmount: 5000.0,
    },
    {
      key: '3',
      repaymentDate: '2020-09-01',
      repaymentType: '员工回款',
      repaymentAmount: 5000.0,
    },
  ]

  // 计算总金额
  const totalAmount = dataSource.reduce((sum, record) => sum + record.repaymentAmount, 0)

  // 表格列定义
  const columns: ColumnsType<RepaymentRecord> = [
    {
      title: '回款日期',
      dataIndex: 'repaymentDate',
      key: 'repaymentDate',
      width: 140,
    },
    {
      title: '回款类型',
      dataIndex: 'repaymentType',
      key: 'repaymentType',
      width: 140,
    },
    {
      title: '回款金额（元）',
      dataIndex: 'repaymentAmount',
      key: 'repaymentAmount',
      width: 140,
      align: 'right',
      render: (amount: number) => amount.toFixed(2),
    },
  ]

  return (
    <Card bodyStyle={{ padding: 0 }} className={styles.repaymentTableCard}>
      {/* 员工还款总额标头 */}
      <div className={styles.headerSection}>
        <Text>员工还款总额</Text>
        <Text>
          {totalAmount.toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </Text>
      </div>
      <Table
        dataSource={data || dataSource}
        columns={columns}
        pagination={false}
        size="small"
        showHeader
      />
    </Card>
  )
}

export default RepaymentManagementTable
