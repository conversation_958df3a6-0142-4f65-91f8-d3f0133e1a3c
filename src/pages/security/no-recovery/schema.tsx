import React from 'react'
import { SchemaType } from '@amazebird/schema-form'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { isMobile } from '@galaxy/utils'
import { keys, map } from 'lodash-es'
import {
  createBaseFieldsSchema,
  createTextAreaField,
  attachmentField,
  EllipsisText,
} from '../utils'
import { detailNumberField } from '../detail/components/schema'

export const getSchema: (type: SchemaConfigType) => SchemaType = (type) => {
  const baseFields = createBaseFieldsSchema()

  const schema: SchemaType = {
    ...baseFields,
    eventCloseAmount: {
      label: '结案金额',
      ...detailNumberField,
    },
    companyActualTotalPaidAmount: {
      label: '公司实际支付总额',
      ...detailNumberField,
    },
    employeePayableAmount: {
      label: '员工应承担金额',
      ...detailNumberField,
    },
    employeeActualPayableAmount: {
      label: '员工实际承担金额',
      ...detailNumberField,
    },
    recoverableAmount: {
      label: '可追偿金额',
      ...detailNumberField,
    },
    recoupedAmount: {
      label: '已追回金额',
      ...detailNumberField,
    },
    waivedRecoveryAmount: {
      label: '放弃追偿金额',
      ...detailNumberField,
    },
    nonRecoveryRequestDescription: createTextAreaField('申请不追偿说明'),
    attachment: {
      ...attachmentField,
    },
  }

  if (type === SchemaConfigType.Detail) {
    map(keys(schema), (key) => {
      schema[key].mode = 'detail'
      schema[key].required = false
      if (schema[key].component !== 'InputNumber') {
        schema[key].renderItem = () => (props) => {
          let ellipsis = !['nonRecoveryRequestDescription'].includes(key)
          if (isMobile()) {
            ellipsis = false
          }
          return <EllipsisText {...props} ellipsis={ellipsis} />
        }
      }
    })
  }
  return schema
}
