import React, { useEffect, useMemo, useState } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { Card, Row } from 'antd'
import { keys, map } from 'lodash-es'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { useStateCenter } from '@amazebird/schema-form'
import { StateCenterDecorator } from '@amazebird/utils'
import AttachmentList from '@/components/AttachmentList'
import { createFormItemConfig, createDetailFormUtils } from '../../utils'
import { getSchema } from '../schema'
import style from '../index.module.less'
import '@amazebird/antd-business-field'

type IProps = {
  form?: any
  ehrDict?: any
  dhrDict?: any
}

function DetailForm(props: IProps) {
  const { form, ehrDict, dhrDict } = props
  const ctx = useStateCenter()
  const schema = useMemo(() => getSchema(SchemaConfigType.Detail), [])
  const { generateFormItemConfig } = useMemo(() => createFormItemConfig('security-no-recovery'), [])
  const { renderItem, renderSingleItem } = useMemo(
    () => createDetailFormUtils(generateFormItemConfig),
    [generateFormItemConfig],
  )
  const [attachmentList, setAttachmentList] = useState([])

  // 监听表单值变化，更新附件列表
  useEffect(() => {
    const currentAttachment = form.getFieldValue('attachment') || []
    setAttachmentList(currentAttachment)
  }, [form])

  // 监听表单数据初始化
  useEffect(() => {
    const updateAttachmentList = () => {
      const currentAttachment = form.getFieldValue('attachment') || []
      setAttachmentList(currentAttachment)
    }

    // 延迟执行以确保表单数据已加载
    const timer = setTimeout(updateAttachmentList, 100)
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    if (ehrDict) {
      ctx.setState({
        ehrDict,
      })
    }
    if (dhrDict) {
      ctx.setState({
        dhrDict,
      })
    }
  }, [ehrDict, dhrDict])

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      const newAttachment = changedValues[key] || []
      setAttachmentList(newAttachment)
    }
  }

  return (
    <SchemaForm
      form={form}
      schema={schema}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 10 }}
      onValuesChange={onValuesChange}
      className={style.noRecoveryForm}
    >
      <Card title="基本信息" size="default">
        <Row gutter={0} align="top">
          {map(
            [
              'eventId',
              'eventUserName',
              'eventCloseAmount',
              'companyActualTotalPaidAmount',
              'employeePayableAmount',
              'employeeActualPayableAmount',
              'recoverableAmount',
              'recoupedAmount',
              'waivedRecoveryAmount',
            ],
            (key) => renderItem(key),
          )}
        </Row>
        {renderSingleItem('nonRecoveryRequestDescription')}
        <AttachmentList fileList={attachmentList} />
      </Card>
    </SchemaForm>
  )
}

export default StateCenterDecorator()(DetailForm)
