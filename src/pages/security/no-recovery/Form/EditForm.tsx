import React, { useEffect, useMemo } from 'react'
import { useStateCenter } from '@amazebird/schema-form'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { StateCenterDecorator } from '@amazebird/utils'
import { getSchema } from '../schema'
import style from '../index.module.less'
import SimpleSecurityForm from '../../components/SimpleSecurityForm'

type IProps = {
  form?: any
  ehrDict?: any
  dhrDict?: any
}

function EditForm(props: IProps) {
  const { form, ehrDict, dhrDict } = props
  const schema = useMemo(() => getSchema(SchemaConfigType.Edit), [])
  const ctx = useStateCenter()

  useEffect(() => {
    if (ehrDict) {
      ctx.setState({
        ehrDict,
      })
    }
    if (dhrDict) {
      ctx.setState({
        dhrDict,
      })
    }
  }, [ehrDict, dhrDict])

  const basicFields = [
    { name: 'eventId' },
    { name: 'eventUserName' },
    { name: 'eventCloseAmount' },
    { name: 'companyActualTotalPaidAmount' },
    { name: 'employeePayableAmount' },
    { name: 'employeeActualPayableAmount' },
    { name: 'recoverableAmount' },
    { name: 'recoupedAmount' },
    { name: 'waivedRecoveryAmount' },
  ]

  return (
    <SimpleSecurityForm
      form={form}
      pageName="security-no-recovery"
      schema={schema}
      className={style.noRecoveryForm}
      basicFields={basicFields}
      descriptionField={{ name: 'nonRecoveryRequestDescription' }}
      attachmentField={{ name: 'attachment' }}
      wrapperClassName={style.innerWrapper}
    />
  )
}

export default StateCenterDecorator()(EditForm)
