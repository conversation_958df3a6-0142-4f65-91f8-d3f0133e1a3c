import React, { useEffect, useRef } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { resolveQuery } from '@galaxy/utils'
import { getNoRecoveryOAById } from '@/api/security-no-recovery'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import PageLoading from '@/components/pageLoading'
import { TOOA_EVENT } from '@/pages/employment-dispute/detail/oa'
import { NoRecoveryDetailForm } from '../Form'
import { transformDataToFormData } from '../Form/utils'

const OANoRecoveryDetail = () => {
  const form = SchemaForm.createForm()
  const query = resolveQuery()
  const contentRef = useRef<HTMLDivElement>(null)
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()

  const setOaPageHeight = () => {
    window.top?.postMessage(
      {
        action: TOOA_EVENT,
        height: contentRef.current?.offsetHeight || 1000,
      },
      '*',
    )
  }

  const getNoRecovery = async () => {
    const res = await getNoRecoveryOAById(query?.id || '')
    const data = res.data || {}
    const values = transformDataToFormData(data)
    form.setFieldsValue({ ...values })
    setTimeout(() => setOaPageHeight())
  }

  useEffect(() => {
    if (!dhrLoading && !ehrLoading) {
      getNoRecovery()
      setOaPageHeight()
    }
  }, [ehrLoading, dhrLoading])

  if (ehrLoading || dhrLoading) {
    return <PageLoading style={{ height: '100vh', backgroundColor: '#fff' }} />
  }

  return (
    <div ref={contentRef}>
      <NoRecoveryDetailForm form={form} ehrDict={ehrDict} dhrDict={dhrDict} />
    </div>
  )
}

export default OANoRecoveryDetail
