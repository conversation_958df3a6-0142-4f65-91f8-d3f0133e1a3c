import React, { useEffect, useState } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { resolveQuery } from '@galaxy/utils'
import { Button, Modal, Space, message } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  getNoRecoveryById,
  createNoRecovery,
  modifyNoRecoveryById,
  saveNoRecoveryDraft,
} from '@/api/security-no-recovery'
import Footer from '@/components/baseContainer/Footer'
import { useLoaderContext } from '@/components/Loader/useLoading'
import PageLoading from '@/components/pageLoading'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import { PAGE_MODE } from '@/constants'
import { scrollToFirstErrorField } from '@/pages/employment-dispute/components/DisputeForm/utils'
import { errorDialog } from '@/components/Dialog'
import { NoRecoveryDetailForm, NoRecoveryForm } from '../Form'
import { transformDataToFormData, transformSubmitData } from '../Form/utils'
import { createFormItemConfig } from '../../utils'

const NoRecoveryDetail = () => {
  const form = SchemaForm.createForm()
  const { loader } = useLoaderContext()
  const query = resolveQuery()
  const navigate = useNavigate()
  const { generateFormItemClass } = createFormItemConfig('security-no-recovery')
  const [pageLoading, setPageLoading] = useState(true)
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()
  const mode = query?.mode || PAGE_MODE.new

  const getNoRecovery = async () => {
    try {
      const res = await getNoRecoveryById(query?.id || '')
      const data = res.data || {}
      const values = transformDataToFormData(data)
      form.setFieldsValue({ ...values })
      setPageLoading(false)
    } catch (e) {
      setPageLoading(false)
    }
  }

  const onSave = async () => {
    try {
      const values = await form.validateFields()
      Modal.confirm({
        title: '提交',
        content: (
          <span>
            确定要提交事件员工
            <strong>{values?.eventUserName || `${query?.userName}(${query?.userNum})`}</strong>
            的不追偿申请？确定后将进入审批流。
          </span>
        ),
        onOk: async () => {
          try {
            const waivedRecoveryAmount = values?.waivedRecoveryAmount
            const recoverableAmount = values?.recoverableAmount
            if (waivedRecoveryAmount <= 0 && recoverableAmount <= 0) {
              errorDialog({
                title: '提交',
                content: '该事件“放弃追偿金额”≤0，且“可追偿金额”≤0，无需提交流程！',
              })
              return
            }
            const params = {
              ...transformSubmitData(values),
            }
            loader?.show()
            const res =
              mode === PAGE_MODE.new
                ? await createNoRecovery(params)
                : await modifyNoRecoveryById(query?.id || '', params)
            if (res.data) {
              message.success('提交成功')
              navigate(-1)
            }
          } catch (error) {
            console.error(error)
          } finally {
            loader?.hide()
          }
        },
      })
    } catch (e) {
      scrollToFirstErrorField({
        errorInfo: e,
        generateFormItemClassFn: generateFormItemClass,
      })
      loader?.hide()
    }
  }

  const onSaveDraft = async () => {
    try {
      loader?.show()
      const values = await form.getFieldsValue()
      const params = {
        ...transformSubmitData(values),
        ...(mode === PAGE_MODE.edit && { id: query?.id }),
      }
      const res = await saveNoRecoveryDraft(params)
      if (res.data) {
        message.success('保存草稿成功')
        navigate(-1)
      }
      loader?.hide()
    } catch (e) {
      loader?.hide()
    }
  }

  const handleCancel = () => {
    Modal.confirm({
      title: '取消',
      content: '数据尚未保存，确定离开当前页面吗？',
      onOk: () => {
        navigate(-1)
      },
    })
  }

  useEffect(() => {
    if (!ehrLoading && !dhrLoading && mode !== PAGE_MODE.new) {
      getNoRecovery()
    }
    if (mode === PAGE_MODE.new) {
      form.setFieldsValue({
        eventId: query?.id,
        eventUserName:
          query?.userName && query?.userNum ? `${query?.userName}(${query?.userNum})` : '',
        eventCloseAmount: query?.eventCloseAmount,
        employeePayableAmount: query?.employeePayableAmount,
        employeeActualPayableAmount: query?.employeeActualPayableAmount,
        recoverableAmount: query?.recoverableAmount,
        recoupedAmount: query?.recoupedAmount,
        waivedRecoveryAmount: query?.waivedRecoveryAmount,
        companyActualTotalPaidAmount: query?.companyActualTotalPaidAmount,
      })
    }
  }, [ehrLoading, dhrLoading])

  if ((ehrLoading || dhrLoading || pageLoading) && mode !== PAGE_MODE.new) {
    return <PageLoading style={{ minHeight: 'calc(100vh - 300px)', backgroundColor: '#fff' }} />
  }

  return (
    <>
      {(mode === PAGE_MODE.edit || mode === PAGE_MODE.new) && (
        <>
          <NoRecoveryForm form={form} ehrDict={ehrDict} dhrDict={dhrDict} />
          <Footer>
            <Space>
              <Button onClick={handleCancel}>取消</Button>
              <Button onClick={onSaveDraft}>保存草稿</Button>
              <Button type="primary" onClick={onSave}>
                提交
              </Button>
            </Space>
          </Footer>
        </>
      )}
      {mode === PAGE_MODE.detail && (
        <NoRecoveryDetailForm form={form} ehrDict={ehrDict} dhrDict={dhrDict} />
      )}
    </>
  )
}

export default NoRecoveryDetail
