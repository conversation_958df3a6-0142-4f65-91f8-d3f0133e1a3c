import React from 'react'
import { message, Modal } from 'antd'
import { useNavigate } from 'react-router-dom'
import { confirmDialog } from '@/components/Dialog'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import { PAGE_MODE, SECURITY_EVENT_PROCESS } from '@/constants'
import { createCompensationCheck } from '@/api/compensation'
import { createSecurityReimbursementCheck } from '@/api/security-reimbursement'
import { createNoRecoveryCheck } from '@/api/security-no-recovery'
import {
  postRevocation,
  postRevocationCancellation,
  postTermination,
  putSafetyDispute,
} from '@/api/securityEventAdminController'
import { pickBy, isNil } from 'lodash-es'
import CaseModal, { CaseModalType } from '../detail/components/Modals/caseModal'

export enum OperatorType {
  detail = 'detail', // 查看
  compensationApply = 'compensation_apply', // 补偿额度申请
  paymentApply = 'payment_apply', // 安全事件付款申请
  loanApply = 'loan_apply', // 安全事件借款申请
  repaymentApproval = 'repayment_approval', // 回款审批
  noRepaymentApply = 'no_repayment_apply', // 不追偿申请
  upgradeToDispute = 'upgrade_to_dispute', // 升级至安全争议
  close = 'close', // 结案
  withdraw = 'withdraw', // 撤案
  cancelWithdraw = 'cancel_withdraw', // 取消撤案
}

export const createParamsWithValues = (params: Record<string, any>) => {
  return pickBy(params, (value) => !isNil(value) && value !== '')
}
interface UseSecurityOperationsProps {
  record?: any
  dict?: any
  action?: any
  isInner?: boolean
}

export const useSecurityOperations = ({
  record,
  dict,
  action,
  isInner = false,
}: UseSecurityOperationsProps = {}) => {
  const navigate = useNavigate()

  const handleUpgradeToDispute = () => {
    confirmDialog({
      title: '升级至安全争议',
      content: (
        <div>
          确定要将
          <span style={{ fontWeight: 'bold' }}>
            {record?.userName}（{record?.userNum}）
          </span>
          升级至安全争议吗？操作不可逆，请谨慎处理！
        </div>
      ),
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const res = await putSafetyDispute(record?.id)
        if (res.data) {
          if (
            [SECURITY_EVENT_PROCESS.closed, SECURITY_EVENT_PROCESS.settled].includes(
              record?.process,
            )
          ) {
            Modal.success({
              title: '升级至安全争议',
              content: (
                <div>
                  <div>升级至安全争议成功</div>
                  <div>点击可前往查看{record?.userName}的安全争议详情</div>
                </div>
              ),
            })
          } else if (record?.process === SECURITY_EVENT_PROCESS.inProgress && !record?.isDisputed) {
            message.success('操作成功')
            if (isInner) {
              window.location.reload()
            } else {
              action?.refresh()
            }
          }
        }
      },
    })
  }

  const handleClose = () => {
    modalWrapperHoc(CaseModal)({
      type: CaseModalType.Close,
      record,
      onOk: async (values: any) => {
        const res = await postTermination(
          { id: record?.id },
          {
            closeDate: values?.closeDate?.valueOf(),
          },
        )
        if (res.data) {
          message.success('结案成功')
          if (isInner) {
            window.location.reload()
          } else {
            action?.refresh()
          }
        }
      },
    })
  }

  const handleWithdraw = () => {
    modalWrapperHoc(CaseModal)({
      type: CaseModalType.Withdraw,
      record,
      dict,
      onOk: async (values: any) => {
        const res = await postRevocation(
          { id: record?.id },
          {
            withdrawReason: values?.withdrawReason,
          },
        )
        if (res.data) {
          message.success('撤案成功')
          if (isInner) {
            window.location.reload()
          } else {
            action?.refresh()
          }
        }
      },
    })
  }

  const handleCancelWithdraw = () => {
    confirmDialog({
      title: '取消撤案',
      content: `确定要将${record?.userName}（${record?.userNum}）的事件进行取消撤案？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const res = await postRevocationCancellation(record?.id)
        if (res.data) {
          message.success('取消撤案成功')
          if (isInner) {
            window.location.reload()
          } else {
            action?.refresh()
          }
        }
      },
    })
  }

  const handleCompensationApply = async () => {
    try {
      await createCompensationCheck({
        eventId: record?.id,
        // 源 1安全事件 2用工争议
        source: 1,
      })
      const searchParams = new URLSearchParams(
        createParamsWithValues({
          mode: PAGE_MODE.new,
          id: record?.id,
          userName: record?.userName,
          userNum: record?.userNum,
          accidentDepartmentName: record?.accidentDepartmentName,
          isDisputed: record?.isDisputed,
          responsibilityAllocation: record?.responsibilityAllocation,
          companyAdvancedAmount: record?.companyAdvancedAmount || 0,
        }),
      )
      navigate(`/security/compensation?${searchParams.toString()}`)
    } catch (error) {
      // 使用全局的message服务端错误信息
    }
  }

  const handlePaymentApply = () => {
    message.info('安全事件付款申请功能开发中...')
  }

  const handleLoanApply = () => {
    message.info('安全事件借款申请功能开发中...')
  }

  const handleRepaymentApproval = async () => {
    try {
      await createSecurityReimbursementCheck(record?.id)
      const searchParams = new URLSearchParams(
        createParamsWithValues({
          mode: PAGE_MODE.new,
          id: record?.id,
          userName: record?.userName,
          userNum: record?.userNum,
          employeePayableAmount: record?.employeePayableAmount || 0,
          employeeActualPayableAmount: record?.employeeActualPayableAmount || 0,
          recoverableAmount: record?.recoverableAmount || 0,
          insuranceEstimatedAmount: record?.insuranceEstimatedAmount || 0,
        }),
      )
      navigate(`/security/reimbursement?${searchParams.toString()}`)
    } catch (error) {
      // 使用全局的message服务端错误信息
    }
  }

  const handleNoRepaymentApply = async () => {
    try {
      await createNoRecoveryCheck(record?.id)
      const searchParams = new URLSearchParams(
        createParamsWithValues({
          mode: PAGE_MODE.new,
          id: record?.id,
          userName: record?.userName,
          userNum: record?.userNum,
          eventCloseAmount: record?.eventCloseAmount || 0,
          employeePayableAmount: record?.employeePayableAmount || 0,
          employeeActualPayableAmount: record?.employeeActualPayableAmount || 0,
          recoverableAmount: record?.recoverableAmount || 0,
          recoupedAmount: record?.recoupedAmount || 0,
          waivedRecoveryAmount: record?.waivedRecoveryAmount || 0,
          companyActualTotalPaidAmount: record?.companyActualTotalPaidAmount || 0,
        }),
      )
      navigate(`/security/no-recovery?${searchParams.toString()}`)
    } catch (error) {
      // 使用全局的message服务端错误信息
    }
  }

  const operatorClick = (key: string) => {
    switch (key) {
      case OperatorType.detail:
        if (record?.id) {
          navigate(`/security/detail?id=${record.id}`)
        }
        break
      case OperatorType.compensationApply:
        handleCompensationApply()
        break
      case OperatorType.paymentApply:
        handlePaymentApply()
        break
      case OperatorType.loanApply:
        handleLoanApply()
        break
      case OperatorType.repaymentApproval:
        handleRepaymentApproval()
        break
      case OperatorType.noRepaymentApply:
        handleNoRepaymentApply()
        break
      case OperatorType.upgradeToDispute:
        handleUpgradeToDispute()
        break
      case OperatorType.close:
        handleClose()
        break
      case OperatorType.withdraw:
        handleWithdraw()
        break
      case OperatorType.cancelWithdraw:
        handleCancelWithdraw()
        break
      default:
        break
    }
  }

  return {
    operatorClick,
    handleUpgradeToDispute,
    handleClose,
    handleWithdraw,
    handleCancelWithdraw,
    handleCompensationApply,
    handlePaymentApply,
    handleLoanApply,
    handleRepaymentApproval,
    handleNoRepaymentApply,
  }
}
