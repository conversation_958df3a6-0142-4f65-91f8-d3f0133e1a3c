import React, { useEffect, useRef, useState } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { resolveQuery } from '@galaxy/utils'
import { getCompensationOAById } from '@/api/compensation'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import PageLoading from '@/components/pageLoading'
import useBatchPackDownload from '@/hooks/useBatchPackDownload'
import moment from 'moment'
import { CompensationDetailForm } from '../Form'
import { transformDataToFormData } from '../Form/utils'

export const TOOA_EVENT = 'dhrPageCompleted'

const OACompensationDetail = () => {
  const form = SchemaForm.createForm()
  const query = resolveQuery()
  const contentRef = useRef<HTMLDivElement>(null)
  const [compensationDetail, setCompensationDetail] = useState<any>(null)
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()
  const { batchPackDownload: batchPackDownloadFn } = useBatchPackDownload()

  const batchPackDownload = () => {
    const attachment = form.getFieldValue('attachment')
    const fileName = `${compensationDetail?.eventUserName}_${
      compensationDetail?.id
    }_${moment().valueOf()}`
    batchPackDownloadFn(attachment, fileName)
  }

  const setOaPageHeight = () => {
    window.top?.postMessage(
      {
        action: TOOA_EVENT,
        height: contentRef.current?.offsetHeight || 1000,
      },
      '*',
    )
  }

  const getCompensation = async () => {
    const res = await getCompensationOAById(query?.id || '')
    const data = res.data || {}
    const values = transformDataToFormData(data)
    form.setFieldsValue({ ...values })
    setCompensationDetail(data)
    setTimeout(() => setOaPageHeight())
  }

  useEffect(() => {
    if (!dhrLoading && !ehrLoading) {
      getCompensation()
      setOaPageHeight()
    }
  }, [ehrLoading, dhrLoading])

  if (ehrLoading || dhrLoading) {
    return <PageLoading style={{ height: '100vh', backgroundColor: '#fff' }} />
  }

  return (
    <div ref={contentRef}>
      <CompensationDetailForm
        form={form}
        ehrDict={ehrDict}
        dhrDict={dhrDict}
        batchPackDownload={batchPackDownload}
      />
    </div>
  )
}

export default OACompensationDetail
