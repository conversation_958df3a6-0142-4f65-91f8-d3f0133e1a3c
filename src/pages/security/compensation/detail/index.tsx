import React, { useEffect, useState } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { resolveQuery } from '@galaxy/utils'
import { Button, Modal, Space, message } from 'antd'
import { useNavigate } from 'react-router-dom'
import Footer from '@/components/baseContainer/Footer'
import { useLoaderContext } from '@/components/Loader/useLoading'
import PageLoading from '@/components/pageLoading'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import useBatchPackDownload from '@/hooks/useBatchPackDownload'
import moment from 'moment'
import { PAGE_MODE } from '@/constants'
import { scrollToFirstErrorField } from '@/pages/employment-dispute/components/DisputeForm/utils'
import {
  getCompensationById as getSecurityCompensationById,
  modifyCompensationById as modifySecurityCompensationById,
  createCompensation as createSecurityCompensation,
  saveCompensationDraft as saveSecurityCompensationDraft,
} from '@/api/compensation'
import { CompensationDetailForm, CompensationForm } from '../Form'
import { transformDataToFormData, transformSubmitData } from '../Form/utils'
import { createFormItemConfig } from '../../utils'

const CompensationDetail = () => {
  const form = SchemaForm.createForm()
  const { loader } = useLoaderContext()
  const query = resolveQuery()
  const navigate = useNavigate()
  const [compensationDetail, setCompensationDetail] = useState<any>()
  const [pageLoading, setPageLoading] = useState(true)
  const { generateFormItemClass } = createFormItemConfig('security-compensation')
  const { batchPackDownload: batchPackDownloadFn } = useBatchPackDownload()
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()
  const mode = query.mode || PAGE_MODE.new

  const batchPackDownload = () => {
    const attachment = form.getFieldValue('attachment')
    const fileName = `${compensationDetail?.eventUserName}_${
      compensationDetail?.id
    }_${moment().valueOf()}`
    batchPackDownloadFn(attachment, fileName)
  }

  const getCompensation = async () => {
    try {
      const res = await getSecurityCompensationById(query?.id || '')
      const data = res.data || {}
      const values = transformDataToFormData(data)
      form.setFieldsValue({ ...values })
      setCompensationDetail(data)
      setPageLoading(false)
    } catch (e) {
      setPageLoading(false)
    }
  }

  const onSave = async () => {
    try {
      const values = await form.validateFields()
      Modal.confirm({
        title: '提交',
        content: (
          <span>
            确定要提交事件员工
            <strong>{values?.eventUserName || `${query?.userName}(${query?.userNum})`}</strong>
            的赔偿额度申请？确定后将进入审批流。
          </span>
        ),
        onOk: async () => {
          try {
            const hasAttachment = values?.attachment?.length > 0
            if (!hasAttachment) {
              message.warning('请上传附件')
              return
            }
            const params = {
              ...transformSubmitData(values),
            }
            loader?.show()
            const res =
              mode === PAGE_MODE.new
                ? await createSecurityCompensation(params)
                : await modifySecurityCompensationById(query?.id || '', params)
            if (res.data) {
              message.success('提交成功')
              navigate(-1)
            }
          } catch (error) {
            console.error(error)
          } finally {
            loader?.hide()
          }
        },
      })
    } catch (e) {
      scrollToFirstErrorField({
        errorInfo: e,
        generateFormItemClassFn: generateFormItemClass,
      })
    }
  }

  const onSaveDraft = async () => {
    try {
      loader?.show()
      const values = await form.getFieldsValue()
      const params = {
        ...transformSubmitData(values),
        ...(mode === PAGE_MODE.edit && { id: query?.id }),
      }
      const res = await saveSecurityCompensationDraft(params)
      if (res.data) {
        message.success('保存草稿成功')
        navigate(-1)
      }
      loader?.hide()
    } catch (e) {
      loader?.hide()
    }
  }

  const handleCancel = () => {
    Modal.confirm({
      title: '取消',
      content: '数据尚未保存，确定离开当前页面吗？',
      onOk: () => {
        navigate(-1)
      },
    })
  }

  useEffect(() => {
    if (!ehrLoading && !dhrLoading && mode !== PAGE_MODE.new) {
      getCompensation()
    }
    if (mode === PAGE_MODE.new) {
      // 解析布尔值字符串
      const isDisputed = query?.isDisputed === 'true'

      form.setFieldsValue({
        eventId: query?.id,
        eventUserName:
          query?.userName && query?.userNum ? `${query?.userName}(${query?.userNum})` : '',
        eventDepartmentName: query?.accidentDepartmentName,
        eventType: isDisputed ? 2 : 1,
        responsibilityAllocation: query?.responsibilityAllocation,
        companyAdvancedAmount: query?.companyAdvancedAmount,
      })
    }
  }, [ehrLoading, dhrLoading])

  if ((ehrLoading || dhrLoading || pageLoading) && mode !== PAGE_MODE.new) {
    return <PageLoading style={{ minHeight: 'calc(100vh - 300px)', backgroundColor: '#fff' }} />
  }

  return (
    <>
      {(mode === PAGE_MODE.edit || mode === PAGE_MODE.new) && (
        <>
          <CompensationForm
            mode={mode}
            form={form}
            ehrDict={ehrDict}
            dhrDict={dhrDict}
            batchPackDownload={batchPackDownload}
          />
          <div style={{ paddingBottom: '70px' }} />
          <Footer>
            <Space>
              <Button onClick={handleCancel}>取消</Button>
              <Button onClick={onSaveDraft}>保存草稿</Button>
              <Button type="primary" onClick={onSave}>
                提交
              </Button>
            </Space>
          </Footer>
        </>
      )}
      {mode === PAGE_MODE.detail && (
        <CompensationDetailForm
          form={form}
          ehrDict={ehrDict}
          dhrDict={dhrDict}
          batchPackDownload={batchPackDownload}
        />
      )}
    </>
  )
}

export default CompensationDetail
