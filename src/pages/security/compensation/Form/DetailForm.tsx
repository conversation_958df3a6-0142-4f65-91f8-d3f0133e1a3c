import React, { useEffect, useMemo, useState } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { <PERSON><PERSON>, Card, Divider, Row } from 'antd'
import { keys, map } from 'lodash-es'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { useStateCenter } from '@amazebird/schema-form'
import { StateCenterDecorator } from '@amazebird/utils'
import { ACCIDENT_NATURE, COMPENSATION_TYPE } from '@/constants'
import AttachmentList from '@/components/AttachmentList'
import { createFormItemConfig, createDetailFormUtils } from '../../utils'
import style from '../index.module.less'
import { getSchema } from '../schema'
import '@amazebird/antd-business-field'

type IProps = {
  form?: any
  ehrDict?: Record<string, any>
  dhrDict?: Record<string, any>
  batchPackDownload?: () => void
}

function DetailForm(props: IProps) {
  const { form, batchPackDownload, ehrDict, dhrDict } = props
  const schema = useMemo(() => getSchema(SchemaConfigType.Detail), [])
  const { generateFormItemConfig } = useMemo(
    () => createFormItemConfig('security-compensation'),
    [],
  )
  const { renderItem, renderSingleItem, renderWideItem } = useMemo(
    () => createDetailFormUtils(generateFormItemConfig),
    [generateFormItemConfig],
  )
  const [hasAttachment, setHasAttachment] = useState(false)
  const [attachmentList, setAttachmentList] = useState([])
  const ctx = useStateCenter()

  // 员工赔偿项目字段
  const employeeCompensationFields = [
    'employeeMedicalExpense',
    'salaryDuringWorkSuspension',
    'oneTimeDisabilitySubsidy',
    'oneTimeWorkInjuryMedicalSubsidy',
    'oneTimeDisabilityEmploymentSubsidy',
    'staffTransportationFee',
    'staffHospitalMealSubsidy',
    'staffDisabilityAidEquipment',
    'staffOtherExpenses',
  ]

  // 三者赔偿项目字段
  const thirdPartyCompensationFields = [
    'thirdPartyMedicalExpense',
    'followUpTreatmentExpense',
    'thirdPartyHospitalMealSubsidy',
    'inpatientNursingFee',
    'outpatientNursingFee',
    'lostWages',
    'nutritionFee',
    'thirdPartyTransportationFee',
    'disabilityCompensation',
    'thirdPartyDisabilityAidEquipment',
    'deathCompensation',
    'funeralExpense',
    'moralCompensation',
    'dependentLivingAllowance',
    'appraisalFee',
    'directPropertyLoss',
    'indirectPropertyLoss',
    'thirdPartyOtherExpenses',
  ]

  // 监听表单值变化，更新附件列表
  useEffect(() => {
    const currentAttachment = form.getFieldValue('attachment') || []
    setAttachmentList(currentAttachment)
    setHasAttachment(currentAttachment.length > 0)
  }, [form])

  // 监听表单数据初始化
  useEffect(() => {
    const updateAttachmentList = () => {
      const currentAttachment = form.getFieldValue('attachment') || []
      setAttachmentList(currentAttachment)
      setHasAttachment(currentAttachment.length > 0)
    }

    // 延迟执行以确保表单数据已加载
    const timer = setTimeout(updateAttachmentList, 100)
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    if (ehrDict) {
      ctx.setState({
        ehrDict,
      })
    }
    if (dhrDict) {
      ctx.setState({
        dhrDict,
      })
    }
  }, [ehrDict, dhrDict])

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      const newAttachment = changedValues[key] || []
      setAttachmentList(newAttachment)
      setHasAttachment(newAttachment.length > 0)
    }
  }

  const EmployeeCompensation = () => {
    const compensationType = form.getFieldValue('compensationType')
    const accidentNature = form.getFieldValue('accidentNature')
    if (
      (compensationType === COMPENSATION_TYPE.employee &&
        accidentNature?.includes(ACCIDENT_NATURE.employeeInjury)) ||
      (compensationType === COMPENSATION_TYPE.thirdParty &&
        (accidentNature?.length > 1 ||
          (accidentNature?.length === 1 && accidentNature[0] !== ACCIDENT_NATURE.thirdPartyDamage)))
    ) {
      return (
        <>
          <div className={style.subTitle}>员工赔偿项目</div>
          <Row gutter={0} align="top">
            {map(employeeCompensationFields, (key) => renderWideItem(key))}
          </Row>
          <Divider />
        </>
      )
    }
    return null
  }

  const ThirdPartyCompensation = () => {
    const compensationType = form.getFieldValue('compensationType')
    if (compensationType === COMPENSATION_TYPE.thirdParty) {
      return (
        <>
          <div className={style.subTitle}>三者赔偿项目</div>
          <Row gutter={0} align="top">
            {map(thirdPartyCompensationFields, (key) => renderItem(key))}
          </Row>
          <Divider />
        </>
      )
    }
    return null
  }

  return (
    <SchemaForm
      form={form}
      schema={schema}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 10 }}
      onValuesChange={onValuesChange}
      className={style.compensationForm}
    >
      <Card title="基本信息" size="default">
        <Row gutter={0} align="top">
          {map(
            [
              'eventId',
              'eventDepartmentName',
              'eventUserName',
              'eventType',
              'responsibilityAllocation',
              'compensationType',
              'accidentNature',
            ],
            (key) => renderItem(key),
          )}
        </Row>
      </Card>

      <Card title="法定赔偿项目" size="default">
        {/* 员工赔偿项目 */}
        <EmployeeCompensation />
        {/* 三者赔偿项目 */}
        <ThirdPartyCompensation />
        {/* 赔偿项目合计 */}
        <div className={style.subTitle}>赔偿项目合计</div>
        <Row gutter={0} align="top">
          {map(['totalMedicalExpense', 'statutoryCompensationAmount'], (key) => renderItem(key))}
        </Row>
      </Card>

      <Card title="调解费用" size="default">
        <Row gutter={0} align="top">
          {map(
            [
              'mediationRequestedAmount',
              'insuranceEstimatedAmount',
              'thirdPartyLiableAmount',
              'workInjuryInsuranceClaimAmount',
              'staffEstimatedPayableAmount',
              'companyEstimatedPayableAmount',
              'staffBorrowedAmount',
              'companyAdvancedAmount',
            ],
            (key) => renderItem(key),
          )}
        </Row>
        {renderSingleItem('compensationDescription')}
      </Card>
      <Card
        title="附件"
        size="default"
        extra={
          <Button
            type="link"
            disabled={!hasAttachment}
            onClick={() => {
              batchPackDownload?.()
            }}
          >
            打包下载
          </Button>
        }
      >
        <AttachmentList fileList={attachmentList} />
      </Card>
    </SchemaForm>
  )
}

export default StateCenterDecorator()(DetailForm)
