import React, { useMemo, useState, useEffect } from 'react'
import { Item, FormItemGrid } from '@amazebird/antd-schema-form'
import { Button, Divider } from 'antd'
import { StateCenterDecorator } from '@amazebird/utils'
import { keys } from 'lodash-es'
import useBatchPackDownload from '@/hooks/useBatchPackDownload'
import { useObserver, useStateCenter } from '@amazebird/schema-form'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { ACCIDENT_NATURE, COMPENSATION_TYPE, PAGE_MODE } from '@/constants'
import { SecurityFormWrapper, FormCard } from '../../components/index'
import { createFormItemConfig } from '../../utils'
import style from '../index.module.less'
import { getSchema } from '../schema'
import '@amazebird/antd-business-field'

type IProps = {
  form?: any
  ehrDict?: Record<string, any>
  dhrDict?: Record<string, any>
  batchPackDownload?: () => void
  mode: string
}

function EditForm(props: IProps) {
  const { form, ehrDict, dhrDict, mode } = props
  const { generateFormItemConfig } = useMemo(
    () => createFormItemConfig('security-compensation'),
    [],
  )
  const [hasAttachment, setHasAttachment] = useState(false)
  const ctx = useStateCenter()
  const { batchPackDownload } = useBatchPackDownload()

  const schema = useMemo(() => getSchema(SchemaConfigType.Edit), [])
  useEffect(() => {
    if (ehrDict) {
      ctx.setState({
        ehrDict,
      })
    }
    if (dhrDict) {
      ctx.setState({
        dhrDict,
      })
    }
  }, [ehrDict, dhrDict])

  useEffect(() => {
    if (form) {
      const values = form.getFieldsValue()
      setHasAttachment(values?.attachment?.length > 0)
    }
  }, [form])

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      if (changedValues[key] && changedValues[key].length > 0 && !hasAttachment) {
        setHasAttachment(true)
      } else if ((!changedValues[key] || changedValues[key].length <= 0) && hasAttachment) {
        setHasAttachment(false)
      }
    }
    if (key === 'compensationType') {
      form.setFieldsValue({
        accidentNature: [],
      })
    }
  }

  // 基本信息字段
  const basicInfoFields = [
    { name: 'eventId' },
    { name: 'eventDepartmentName' },
    { name: 'eventUserName' },
    { name: 'eventType' },
    { name: 'responsibilityAllocation' },
    { name: 'compensationType' },
    { name: 'accidentNature' },
  ]

  // 员工赔偿项目字段
  const employeeCompensationFields = [
    { name: 'employeeMedicalExpense' },
    { name: 'salaryDuringWorkSuspension' },
    { name: 'oneTimeDisabilitySubsidy' },
    { name: 'oneTimeWorkInjuryMedicalSubsidy' },
    { name: 'oneTimeDisabilityEmploymentSubsidy' },
    { name: 'staffTransportationFee' },
    { name: 'staffHospitalMealSubsidy' },
    { name: 'staffDisabilityAidEquipment' },
    { name: 'staffOtherExpenses' },
  ]

  // 三者赔偿项目字段
  const thirdPartyCompensationFields = [
    { name: 'thirdPartyMedicalExpense' },
    { name: 'followUpTreatmentExpense' },
    { name: 'thirdPartyHospitalMealSubsidy' },
    { name: 'inpatientNursingFee' },
    { name: 'outpatientNursingFee' },
    { name: 'lostWages' },
    { name: 'nutritionFee' },
    { name: 'thirdPartyTransportationFee' },
    { name: 'disabilityCompensation' },
    { name: 'thirdPartyDisabilityAidEquipment' },
    { name: 'deathCompensation' },
    { name: 'funeralExpense' },
    { name: 'moralCompensation' },
    { name: 'dependentLivingAllowance' },
    { name: 'appraisalFee' },
    { name: 'directPropertyLoss' },
    { name: 'indirectPropertyLoss' },
    { name: 'thirdPartyOtherExpenses' },
  ]

  // 赔偿合计字段
  const compensationTotalFields = [
    { name: 'totalMedicalExpense' },
    { name: 'statutoryCompensationAmount' },
  ]

  // 调解费用字段
  const mediationFeeFields = [
    { name: 'mediationRequestedAmount' },
    { name: 'insuranceEstimatedAmount' },
    { name: 'thirdPartyLiableAmount' },
    { name: 'workInjuryInsuranceClaimAmount' },
    { name: 'staffEstimatedPayableAmount' },
    { name: 'companyEstimatedPayableAmount' },
    { name: 'staffBorrowedAmount' },
    { name: 'companyAdvancedAmount' },
  ]

  const EmployeeCompensation = () => {
    const [compensationType, accidentNature] = useObserver(['compensationType', 'accidentNature'])
    const compensationTypeParam =
      mode === PAGE_MODE.new ? compensationType : form.getFieldValue('compensationType')
    const accidentNatureParam =
      mode === PAGE_MODE.new ? accidentNature : form.getFieldValue('accidentNature')

    // （1）「赔付类型」=仅员工，「事故性质」=员工受伤，展示员工赔偿项目
    // （2）「赔付类型」=涉三者，「事故性质」不只有三方财损，展示员工赔偿项目
    const shouldShowEmployeeCompensation =
      (compensationTypeParam === COMPENSATION_TYPE.employee &&
        accidentNatureParam?.includes(ACCIDENT_NATURE.employeeInjury)) ||
      (compensationTypeParam === COMPENSATION_TYPE.thirdParty &&
        accidentNatureParam?.length > 0 &&
        !(
          accidentNatureParam?.length === 1 &&
          accidentNatureParam[0] === ACCIDENT_NATURE.thirdPartyDamage
        ))

    if (shouldShowEmployeeCompensation) {
      return (
        <div className={style.employeeCompensation}>
          <div className={style.subTitle}>员工赔偿项目</div>
          <FormItemGrid fillEmpty colCount={2}>
            {employeeCompensationFields.map((field) => (
              <Item key={field.name} {...generateFormItemConfig(field.name)} />
            ))}
          </FormItemGrid>
          <Divider />
        </div>
      )
    }
    return null
  }

  const ThirdPartyCompensation = () => {
    const [compensationType, accidentNature] = useObserver(['compensationType', 'accidentNature'])
    const compensationTypeParam =
      mode === PAGE_MODE.new ? compensationType : form.getFieldValue('compensationType')
    const accidentNatureParam =
      mode === PAGE_MODE.new ? accidentNature : form.getFieldValue('accidentNature')

    // 「赔付类型」=涉三者时，展示三者赔偿项目
    // - 「事故性质」只有三方财损，仅展示三者赔偿项目
    // - 「事故性质」不只有三方财损，展示三者赔偿项目（同时展示员工赔偿项目）
    const shouldShowThirdPartyCompensation =
      compensationTypeParam === COMPENSATION_TYPE.thirdParty && accidentNatureParam?.length > 0

    if (shouldShowThirdPartyCompensation) {
      return (
        <div className={style.thirdPartyCompensation}>
          <div className={style.subTitle}>三者赔偿项目</div>
          <FormItemGrid fillEmpty colCount={2}>
            {thirdPartyCompensationFields.map((field) => (
              <Item key={field.name} {...generateFormItemConfig(field.name)} />
            ))}
          </FormItemGrid>
          <Divider />
        </div>
      )
    }
    return null
  }

  return (
    <SecurityFormWrapper
      form={form}
      pageName="security-compensation"
      schema={schema}
      className={style.compensationForm}
      onValuesChange={onValuesChange}
      attachmentExtra={
        <Button
          type="link"
          disabled={!hasAttachment}
          onClick={() => {
            const attachment = form.getFieldValue('attachment')
            const fileName = 'compensation'
            batchPackDownload(attachment, fileName)
          }}
        >
          打包下载
        </Button>
      }
    >
      {/* 基本信息卡片 */}
      <FormCard
        title="基本信息"
        fields={basicInfoFields}
        generateFormItemConfig={generateFormItemConfig}
      />

      {/* 法定赔偿项目卡片 */}
      <FormCard title="法定赔偿项目" fields={[]} generateFormItemConfig={generateFormItemConfig}>
        {/* 员工赔偿项目 */}
        <EmployeeCompensation />
        {/* 三者赔偿项目 */}
        <ThirdPartyCompensation />
        {/* 赔偿项目合计 */}
        <div className={style.subTitle}>赔偿项目合计</div>
        <FormItemGrid fillEmpty colCount={2}>
          {compensationTotalFields.map((field) => (
            <Item key={field.name} {...generateFormItemConfig(field.name)} />
          ))}
        </FormItemGrid>
      </FormCard>

      {/* 调解费用卡片 */}
      <FormCard
        title="调解费用"
        fields={mediationFeeFields}
        generateFormItemConfig={generateFormItemConfig}
      >
        {/* 赔偿情况说明 */}
        <Item
          {...generateFormItemConfig('compensationDescription')}
          wrapperCol={{ span: 17 }}
          labelCol={{ span: 4 }}
        />
      </FormCard>
    </SecurityFormWrapper>
  )
}

export default StateCenterDecorator()(EditForm)
