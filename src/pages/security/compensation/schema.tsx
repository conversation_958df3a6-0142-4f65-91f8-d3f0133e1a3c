import React from 'react'
import { Observer, SchemaType } from '@amazebird/schema-form'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { keys, map } from 'lodash-es'
import { isMobile } from '@galaxy/utils'
import {
  ACCIDENT_NATURE,
  COMPENSATION_TYPE,
  EVENT_TYPE_OPTIONS,
  RESPONSIBILITY_ALLOCATION,
} from '@/constants'
import { getStaffFeeCoefficient } from '@/api/compensation'
import {
  textField,
  numberAmountField,
  createTextAreaField,
  attachmentField,
  EllipsisText,
} from '../utils'
import { detailNumberField } from '../detail/components/schema'

const ObserverFieldArray = [
  'employeeMedicalExpense',
  'salaryDuringWorkSuspension',
  'oneTimeDisabilitySubsidy',
  'oneTimeWorkInjuryMedicalSubsidy',
  'oneTimeDisabilityEmploymentSubsidy',
  'staffTransportationFee',
  'staffHospitalMealSubsidy',
  'staffDisabilityAidEquipment',
  'staffOtherExpenses',
  'thirdPartyMedicalExpense',
  'followUpTreatmentExpense',
  'thirdPartyHospitalMealSubsidy',
  'inpatientNursingFee',
  'outpatientNursingFee',
  'lostWages',
  'nutritionFee',
  'thirdPartyTransportationFee',
  'disabilityCompensation',
  'thirdPartyDisabilityAidEquipment',
  'deathCompensation',
  'funeralExpense',
  'moralCompensation',
  'dependentLivingAllowance',
  'appraisalFee',
  'directPropertyLoss',
  'indirectPropertyLoss',
  'thirdPartyOtherExpenses',
]

// 将负数处理为0的工具函数
const convertNegativeToZero = (value: number | undefined | null): number => {
  const num = value || 0
  return num < 0 ? 0 : num
}

// 计算数组总和，负数处理为0
const sumWithNegativeAsZero = (values: (number | undefined | null)[]): number => {
  return Number(
    values
      .map(convertNegativeToZero)
      .reduce((acc, curr) => acc + curr, 0)
      .toFixed(2),
  )
}

type SchemaConfigFunc = (type: SchemaConfigType) => SchemaType
export const getSchema: SchemaConfigFunc = (type) => {
  const schema: SchemaType = {
    eventId: {
      label: '事件ID',
      ...textField,
    },
    eventDepartmentName: {
      label: '事件部门',
      ...textField,
    },
    eventUserName: {
      label: '事件员工姓名',
      ...textField,
    },
    eventType: {
      label: '事件类型',
      component: 'Select',
      mode: 'detail',
      options: EVENT_TYPE_OPTIONS,
    },
    responsibilityAllocation: {
      label: '责任划分',
      ...textField,
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_RESPONSIBILITY?.list || []
          }
          return []
        },
      }),
    },
    compensationType: {
      label: '赔付类型',
      component: 'Select',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_COMPENSATION_TYPE?.list || []
          }
          return []
        },
      }),
      props: {
        allowClear: true,
        placeholder: '请选择赔付类型',
      },
    },
    accidentNature: {
      label: '事故性质',
      name: 'accidentNature',
      component: 'Select',
      options: Observer({
        watch: ['#dhrDict', 'compensationType'],
        action: ([v, compensationType]) => {
          if (v) {
            const list = v?.THE_ACCIDENT_NATURE?.list || []
            if (compensationType === COMPENSATION_TYPE.employee) {
              return list.filter((item) => item.code === ACCIDENT_NATURE.employeeInjury)
            }
            return list
          }
          return []
        },
      }),
      props: {
        mode: 'multiple',
        allowClear: true,
      },
      placeholder: '请选择事故性质',
    },
    employeeMedicalExpense: {
      label: '员工医疗费',
      ...numberAmountField,
    },
    salaryDuringWorkSuspension: {
      label: '停工留薪期间的工资（含补差）',
      ...numberAmountField,
    },
    oneTimeDisabilitySubsidy: {
      label: '一次性伤残补助金（含补差）',
      ...numberAmountField,
    },
    oneTimeWorkInjuryMedicalSubsidy: {
      label: '一次性工伤医疗补助金',
      ...numberAmountField,
    },
    oneTimeDisabilityEmploymentSubsidy: {
      label: '一次性伤残就业补助金',
      ...numberAmountField,
    },
    staffTransportationFee: {
      label: '员工交通费',
      ...numberAmountField,
    },
    staffHospitalMealSubsidy: {
      label: '员工住院伙食补助费',
      ...numberAmountField,
    },
    staffDisabilityAidEquipment: {
      label: '员工残疾辅助器具',
      ...numberAmountField,
    },
    staffOtherExpenses: {
      label: '员工其他费用',
      ...numberAmountField,
    },
    thirdPartyMedicalExpense: {
      label: '三方医疗费',
      ...numberAmountField,
    },
    followUpTreatmentExpense: {
      label: '后续治疗费',
      ...numberAmountField,
    },
    thirdPartyHospitalMealSubsidy: {
      label: '三方住院伙食补助费',
      ...numberAmountField,
    },
    inpatientNursingFee: {
      label: '住院护理费',
      ...numberAmountField,
    },
    outpatientNursingFee: {
      label: '院外护理费',
      ...numberAmountField,
    },
    lostWages: {
      label: '误工费',
      ...numberAmountField,
    },
    nutritionFee: {
      label: '营养费',
      ...numberAmountField,
    },
    thirdPartyTransportationFee: {
      label: '三方交通费',
      ...numberAmountField,
    },
    disabilityCompensation: {
      label: '残疾赔偿金',
      ...numberAmountField,
    },
    thirdPartyDisabilityAidEquipment: {
      label: '三方残疾辅助器具',
      ...numberAmountField,
    },
    deathCompensation: {
      label: '死亡赔偿金',
      ...numberAmountField,
    },
    funeralExpense: {
      label: '丧葬费',
      ...numberAmountField,
    },
    moralCompensation: {
      label: '精神抚慰金',
      ...numberAmountField,
    },
    dependentLivingAllowance: {
      label: '被抚养人生活费',
      ...numberAmountField,
    },
    appraisalFee: {
      label: '鉴定费用',
      ...numberAmountField,
    },
    directPropertyLoss: {
      label: '直接财产损失',
      ...numberAmountField,
    },
    indirectPropertyLoss: {
      label: '间接财产损失',
      ...numberAmountField,
    },
    thirdPartyOtherExpenses: {
      label: '三方其他费用',
      ...numberAmountField,
    },
    totalMedicalExpense: {
      label: '医疗费总额',
      ...detailNumberField,
      value: Observer({
        watch: ['employeeMedicalExpense', 'thirdPartyMedicalExpense'],
        action: ([employeeMedicalExpense, thirdPartyMedicalExpense]) => {
          return sumWithNegativeAsZero([employeeMedicalExpense, thirdPartyMedicalExpense])
        },
      }),
    },
    statutoryCompensationAmount: {
      label: '法定赔偿金额',
      value: Observer({
        watch: ObserverFieldArray,
        action: (v) => {
          return sumWithNegativeAsZero(v)
        },
      }),
      ...detailNumberField,
    },
    mediationRequestedAmount: {
      label: '案件申请调解金额',
      ...numberAmountField,
    },
    insuranceEstimatedAmount: {
      label: '保险预估金额',
      ...numberAmountField,
    },
    thirdPartyLiableAmount: {
      label: '第三方承担金额',
      ...numberAmountField,
    },
    workInjuryInsuranceClaimAmount: {
      label: '工伤保险理赔金额',
      ...numberAmountField,
    },
    staffEstimatedPayableAmount: {
      label: '员工预估承担金额',
      value: Observer({
        watch: [
          'responsibilityAllocation',
          'compensationType',
          'mediationRequestedAmount',
          'insuranceEstimatedAmount',
          'thirdPartyLiableAmount',
        ],
        action: async ([
          responsibilityAllocation,
          compensationType,
          mediationRequestedAmount,
          insuranceEstimatedAmount,
          thirdPartyLiableAmount,
          // eslint-disable-next-line consistent-return
        ]) => {
          if (
            [
              RESPONSIBILITY_ALLOCATION.employeeNoResponsibility,
              RESPONSIBILITY_ALLOCATION.bothNoResponsibility,
              RESPONSIBILITY_ALLOCATION.negotiation,
            ].includes(responsibilityAllocation) ||
            compensationType === COMPENSATION_TYPE.employee
          ) {
            return 0
          }
          if (
            responsibilityAllocation !== RESPONSIBILITY_ALLOCATION.employeeNoResponsibility &&
            compensationType === COMPENSATION_TYPE.thirdParty
          ) {
            const { data: Coefficient } = await getStaffFeeCoefficient(responsibilityAllocation)

            // 确保所有参与计算的值都不是undefined，避免NaN
            const safeMediation = mediationRequestedAmount < 0 ? 0 : mediationRequestedAmount || 0
            const safeInsurance = insuranceEstimatedAmount < 0 ? 0 : insuranceEstimatedAmount || 0
            const safeThirdParty = thirdPartyLiableAmount < 0 ? 0 : thirdPartyLiableAmount || 0

            /**
             * ①若案件申请调解金额一保险预估金额＞2000，则员工预估承担金额=（案件申请调解金额一保险预估金额—2000）*系数 +2000—第三方承担金额
             * ②若案件申请调解金额一保险预估金额≤2000.则员工预估承担金额=案件申请调解金额一保险预估金额一第三方承担金额
             */
            let result
            if (safeMediation - safeInsurance > 2000) {
              result = (safeMediation - safeInsurance - 2000) * Coefficient + 2000 - safeThirdParty
            } else {
              result = safeMediation - safeInsurance - safeThirdParty
            }
            // 如果计算结果是负数或NaN，则改为0
            return result < 0 || Number.isNaN(result) ? 0 : result
          }
        },
      }),
      ...detailNumberField,
    },
    companyEstimatedPayableAmount: {
      label: '公司预估承担金额',
      value: Observer({
        // 公司预估承担金额=案件申请调解金额一保险预估金额一员工预估承担金额一第三方承担金额一工伤保险理赔金额
        watch: [
          'mediationRequestedAmount',
          'insuranceEstimatedAmount',
          'staffEstimatedPayableAmount',
          'thirdPartyLiableAmount',
          'workInjuryInsuranceClaimAmount',
        ],
        action: ([
          mediationRequestedAmount,
          insuranceEstimatedAmount,
          staffEstimatedPayableAmount,
          thirdPartyLiableAmount,
          workInjuryInsuranceClaimAmount,
        ]) => {
          const result = Number(
            (
              (mediationRequestedAmount < 0 ? 0 : mediationRequestedAmount || 0) -
              (insuranceEstimatedAmount < 0 ? 0 : insuranceEstimatedAmount || 0) -
              (staffEstimatedPayableAmount < 0 ? 0 : staffEstimatedPayableAmount || 0) -
              (thirdPartyLiableAmount < 0 ? 0 : thirdPartyLiableAmount || 0) -
              (workInjuryInsuranceClaimAmount < 0 ? 0 : workInjuryInsuranceClaimAmount || 0)
            ).toFixed(2),
          )
          if (result < 0) {
            return 0
          }
          return result
        },
      }),
      ...detailNumberField,
    },
    staffBorrowedAmount: {
      label: '员工已借金额',
      ...detailNumberField,
    },
    companyAdvancedAmount: {
      label: '公司已垫付金额',
      ...detailNumberField,
    },
    compensationDescription: createTextAreaField('赔偿情况说明'),
    attachment: {
      ...attachmentField,
      label: '',
    },
  }
  if (type === SchemaConfigType.Detail) {
    map(keys(schema), (key) => {
      schema[key].mode = 'detail'
      schema[key].required = false
      if (schema[key].component !== 'InputNumber' && schema[key].name !== 'accidentNature') {
        schema[key].renderItem = () => (props) => {
          let ellipsis = !['compensationDescription'].includes(key)
          if (isMobile()) {
            ellipsis = false
          }
          return <EllipsisText {...props} ellipsis={ellipsis} />
        }
      }
    })
  }

  return schema
}
