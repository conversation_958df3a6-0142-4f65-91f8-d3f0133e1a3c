import React from 'react'
import TablePermissionAction, {
  PermissionActionItem,
} from '@/components/businessPermission/TablePermissionAction'
import { SECURITY } from '@/constants/rbac-code/security'
import { SECURITY_EVENT_PROCESS } from '@/constants'
import { useSecurityOperations, OperatorType } from './hooks/useSecurityOperations'

type IProps = {
  expandNumber?: number
  record: any
  dict: any
  action: any
}

/**
 * 判断是否可以升级至安全争议
 */
export const canUpgradeToDispute = (record: any): boolean => {
  const { process, isDisputed } = record || {}
  // 跟进中且无争议
  if (process === SECURITY_EVENT_PROCESS.inProgress && !isDisputed) {
    return true
  }
  // 已结案或已结算
  if (process === SECURITY_EVENT_PROCESS.closed || process === SECURITY_EVENT_PROCESS.settled) {
    return true
  }
  return false
}

/**
 * 判断是否为进行中状态
 */
export const isInProgress = (record: any): boolean => {
  return record?.process === SECURITY_EVENT_PROCESS.inProgress
}

/**
 * 判断是否为已撤案状态
 */
export const isWithdrawn = (record: any): boolean => {
  return record?.process === SECURITY_EVENT_PROCESS.withdrawn
}

const TableActions = (props: IProps) => {
  const { expandNumber, record, dict, action } = props
  const { operatorClick } = useSecurityOperations({ record, dict, action })

  const getOperatorColumns = () => {
    // TODO: 修改权限项编码
    const actions: PermissionActionItem[] = [
      {
        key: OperatorType.detail,
        label: '查看',
        order: 1,
        permissionCode: SECURITY.DETAIL,
      },
      {
        key: OperatorType.compensationApply,
        label: '赔偿额度申请',
        order: 2,
        permissionCode: SECURITY.DETAIL,
        disabled: !isInProgress(record),
      },
      {
        key: OperatorType.paymentApply,
        label: '三方付款申请',
        order: 3,
        permissionCode: SECURITY.DETAIL,
      },
      {
        key: OperatorType.loanApply,
        label: '三方借款申请',
        order: 4,
        permissionCode: SECURITY.DETAIL,
      },
      {
        key: OperatorType.repaymentApproval,
        label: '回款审批',
        order: 5,
        permissionCode: SECURITY.DETAIL,
        disabled: !isInProgress(record),
      },
      {
        key: OperatorType.noRepaymentApply,
        label: '不追偿申请',
        order: 6,
        permissionCode: SECURITY.DETAIL,
        disabled: !isInProgress(record),
      },
      {
        key: OperatorType.upgradeToDispute,
        label: '升级至安全争议',
        order: 6,
        permissionCode: SECURITY.DETAIL,
        disabled: !canUpgradeToDispute(record),
      },
      {
        key: OperatorType.close,
        label: '结案',
        order: 7,
        permissionCode: SECURITY.DETAIL,
        disabled: !isInProgress(record),
      },
      {
        key: OperatorType.withdraw,
        label: '撤案',
        order: 8,
        permissionCode: SECURITY.DETAIL,
        disabled: !isInProgress(record),
      },
      {
        key: OperatorType.cancelWithdraw,
        label: '取消撤案',
        order: 9,
        permissionCode: SECURITY.DETAIL,
        disabled: !isWithdrawn(record),
      },
    ]

    return actions
  }

  return (
    <TablePermissionAction
      expandNumber={expandNumber}
      actions={getOperatorColumns()}
      menuClick={operatorClick}
    />
  )
}

export default TableActions
