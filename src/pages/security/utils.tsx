import React, { useState, useEffect } from 'react'
import { uuid, isMobile } from '@galaxy/utils'
import { SchemaType } from '@amazebird/schema-form'
import { isNil, map, filter, isObject } from 'lodash-es'
import moment from 'moment'
import { Typography, message, Col, Button } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'
import { Item } from '@amazebird/antd-schema-form'
import { OSS_CONFIG } from '@/constants'
import { uploadSdk } from '@/services/upload'
import { processFileWatermark } from '@/utils/waterMark/WaterMarkFile'
import style from './style.module.less'

const { Text } = Typography

// 文件项类型定义
type FileItem = {
  name: string
  uuid: string
}

// 全局水印文件缓存
const WATERMARK_CACHE = new Map<string, { url: string; isProcessing: boolean }>()

// 清理缓存的工具函数（可选使用）
export const clearWatermarkCache = () => {
  WATERMARK_CACHE.forEach((cache) => {
    if (cache.url && cache.url.startsWith('blob:')) {
      URL.revokeObjectURL(cache.url)
    }
  })
  WATERMARK_CACHE.clear()
}

// 带水印的文件项组件
const WatermarkedFileItem: React.FC<{ file: FileItem }> = ({ file }) => {
  const [fileUrl, setFileUrl] = useState<string>('')
  const [isProcessing, setIsProcessing] = useState(true)

  useEffect(() => {
    // 检查缓存
    const cached = WATERMARK_CACHE.get(file.uuid)
    if (cached) {
      setFileUrl(cached.url)
      setIsProcessing(cached.isProcessing)

      // 如果缓存中是处理中状态，需要监听处理完成
      if (cached.isProcessing) {
        const checkProcessing = setInterval(() => {
          const updatedCache = WATERMARK_CACHE.get(file.uuid)
          if (updatedCache && !updatedCache.isProcessing) {
            setFileUrl(updatedCache.url)
            setIsProcessing(false)
            clearInterval(checkProcessing)
          }
        }, 100)

        return () => clearInterval(checkProcessing)
      }
      return undefined
    }

    // 设置缓存为处理中状态
    WATERMARK_CACHE.set(file.uuid, { url: '', isProcessing: true })

    const processFile = async () => {
      try {
        // 下载原始文件
        const result = await uploadSdk.download(file.uuid)
        if (!result?.url) {
          WATERMARK_CACHE.set(file.uuid, { url: '', isProcessing: false })
          setIsProcessing(false)
          return
        }

        // 处理水印
        const watermarkResult = await processFileWatermark({
          fileUrl: result.url,
          uuid: file.uuid,
          fileName: file.name,
        })

        let finalUrl = ''
        if (watermarkResult.error) {
          console.warn(`文件水印处理失败: ${file.name}`, watermarkResult.error)
          // 水印处理失败时使用原始文件
          finalUrl = result.url
        } else {
          finalUrl = watermarkResult.processedUrl
        }

        // 更新缓存
        WATERMARK_CACHE.set(file.uuid, { url: finalUrl, isProcessing: false })
        setFileUrl(finalUrl)
      } catch (error) {
        console.error('处理文件失败:', error)
        WATERMARK_CACHE.set(file.uuid, { url: '', isProcessing: false })
      } finally {
        setIsProcessing(false)
      }
    }

    processFile()

    return undefined
  }, [file.uuid, file.name])

  const handleClick = () => {
    if (fileUrl && !isProcessing) {
      window.open(fileUrl, '_blank')
    }
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      {isProcessing && <LoadingOutlined style={{ marginRight: 8, color: '#1890ff' }} />}
      <Button
        type="link"
        style={{
          padding: 0,
          height: 'auto',
          textAlign: 'start',
        }}
        onClick={handleClick}
        size="small"
        disabled={isProcessing}
      >
        {isProcessing ? '正在加载中...' : file.name}
      </Button>
    </div>
  )
}

// 存储每个pageName对应的UUID，确保同一个pageName使用相同的UUID
const PAGE_NAME_UUID_MAP = new Map<string, string>()

/** 柯里化函数：先接收pageName，返回携带该pageName的generateFormItemConfig函数 */
export const createFormItemConfig = (pageName: string) => {
  // 如果该pageName还没有UUID，则生成一个
  if (!PAGE_NAME_UUID_MAP.has(pageName)) {
    PAGE_NAME_UUID_MAP.set(pageName, uuid())
  }

  // 使用固定的UUID
  const FORM_ITEM_CLASS_PREFIX = `form-item-${pageName}_${PAGE_NAME_UUID_MAP.get(pageName)}__`

  /** 生成form item class */
  const generateFormItemClass = (name: string) => `${FORM_ITEM_CLASS_PREFIX}${name}`

  /** 生成item配置 */
  const generateFormItemConfig = (name: string) => ({
    className: generateFormItemClass(name),
    field: name,
  })

  return { generateFormItemClass, generateFormItemConfig }
}

// 通用组件和配置
type EllipsisTextProps = {
  value?: any
  format?: string
  options?: any[]
  ellipsis?: boolean
  [key: string]: any
}

export const EllipsisText = (props: EllipsisTextProps) => {
  const ellipsis = isNil(props?.ellipsis) ? true : props.ellipsis
  let text = props.value
  if (props?.format && text) {
    text = moment(props.value).format(props?.format)
  } else if (props?.options && props.options.length > 0) {
    text = map(
      filter(props?.options, (item) => item.value === props.value),
      (item) => item.label,
    ).join('；')
  } else if (isObject(text) && 'label' in text) {
    text = text.label
  }
  return <Text ellipsis={ellipsis ? { tooltip: text || '--' } : false}> {text || '--'}</Text>
}

// 通用字段配置
export const textField = {
  mode: 'detail',
  renderItem: () => (props) => {
    return <EllipsisText {...props} />
  },
}

export const numberAmountField = {
  component: 'InputNumber',
  max: 10000000,
  min: 0,
  props: {
    addonAfter: '元',
    precision: 2,
  },
  required: true,
}

// 通用附件上传配置
export const attachmentField = {
  component: 'Upload',
  label: '附件',
  visible: true,
  props: {
    oss: OSS_CONFIG,
    clientCode: 'GLOBAL',
    listType: 'text',
    accept:
      'image/jpg,image/jpeg,image/png,application/pdf,application/x-rar-compressed,application/zip',
    fileSizeLimit: 50,
    onChange: (fileList) => {
      if (fileList.length === 10) {
        message.warn('最多支持上传10个文件')
      }
    },
    fileNameRender: (_originNode, file, _fileList) => {
      // 创建一个处理单个文件水印的组件
      return <WatermarkedFileItem file={file} />
    },
    maxNum: 10,
    remark: '支持扩展名：pdf、jpg、jpeg、png、rar、zip，单个文件不超过 50M',
  },
}

// 基础字段Schema生成器
export const createBaseFieldsSchema = (): Partial<SchemaType> => ({
  eventId: {
    label: '事件ID',
    ...textField,
  },
  eventUserName: {
    label: '事件员工姓名',
    ...textField,
  },
})

// 文本域字段生成器
export const createTextAreaField = (label: string, required = true, maxLength = 500) => ({
  component: 'Input.TextArea',
  label,
  required,
  props: {
    maxLength,
    showCount: false,
  },
})

// DetailForm 通用工具函数
export const createDetailFormUtils = (generateFormItemConfig: (name: string) => any) => {
  const basicProps = {
    labelCol: { flex: '135px' },
    ...(isMobile() ? { wrapperCol: { flex: '1' } } : { wrapperCol: { span: 17 } }),
  }

  const renderItem = (key: string) => {
    return (
      <Col xs={24} md={12} key={key} className={style.emptyItem}>
        <Item {...generateFormItemConfig(key)} {...basicProps} />
      </Col>
    )
  }

  const renderWideItem = (key: string) => {
    return (
      <Col xs={24} md={12} key={key}>
        <Item {...generateFormItemConfig(key)} {...basicProps} labelCol={{ flex: '200px' }} />
      </Col>
    )
  }

  const renderSingleItem = (key: string) => {
    return <Item {...generateFormItemConfig(key)} {...basicProps} />
  }

  return {
    basicProps,
    renderItem,
    renderWideItem,
    renderSingleItem,
  }
}
