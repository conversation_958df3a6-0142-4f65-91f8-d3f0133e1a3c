import React, { useEffect, useState } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { resolveQuery } from '@galaxy/utils'
import { Button, Modal, Space, message } from 'antd'
import { useNavigate } from 'react-router-dom'
import {
  getSecurityReimbursementById,
  modifySecurityReimbursementById,
  createSecurityReimbursement,
  saveSecurityReimbursementDraft,
} from '@/api/security-reimbursement'
import { getDminStaffRelationV1SecurityEventById as getSecurityEventById } from '@/api/securityEventAdminController'
import Footer from '@/components/baseContainer/Footer'
import { useLoaderContext } from '@/components/Loader/useLoading'
import PageLoading from '@/components/pageLoading'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import { PAGE_MODE, REPAYMENT_TYPE } from '@/constants'
import { scrollToFirstErrorField } from '@/pages/employment-dispute/components/DisputeForm/utils'
import { errorDialog } from '@/components/Dialog'
import moment from 'moment'
import { ReimbursementDetailForm, ReimbursementForm } from '../Form'
import { transformDataToFormData, transformSubmitData } from '../Form/utils'
import { createFormItemConfig } from '../../utils'

const ReimbursementDetail = () => {
  const form = SchemaForm.createForm()
  const { loader } = useLoaderContext()
  const query = resolveQuery()
  const navigate = useNavigate()
  const { generateFormItemClass } = createFormItemConfig('security-reimbursement')
  const [pageLoading, setPageLoading] = useState(true)
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()
  const mode = query?.mode || PAGE_MODE.new

  const getReimbursement = async () => {
    try {
      const res = await getSecurityReimbursementById(query?.id || '')
      const data = res.data || {}
      const values = transformDataToFormData(data)
      form.setFieldsValue({ ...values })
      setPageLoading(false)
    } catch (e) {
      setPageLoading(false)
    }
  }

  const onSave = async () => {
    try {
      const values = await form.validateFields()
      Modal.confirm({
        title: '提交',
        content: (
          <span>
            确定要提交事件员工
            <strong>{values?.eventUserName || `${query?.userName}(${query?.userNum})`}</strong>
            的回款审批？确定后将进入审批流。
          </span>
        ),
        onOk: async () => {
          try {
            const hasAttachment = values?.attachment?.length > 0
            if (!hasAttachment) {
              message.warning('请上传附件')
              return
            }
            const { data: securityDetail } = await getSecurityEventById({
              id: values?.eventId,
            })
            const paymentReceivedType = values?.paymentReceivedType // 回款类型
            if (
              paymentReceivedType === REPAYMENT_TYPE.employee &&
              securityDetail?.statutoryCompensationAmount >= 0
            ) {
              errorDialog({
                title: '提交',
                content:
                  '该事件“法定赔偿金额”<0，无法提交！请先完成【赔偿额度申请】流程，再提交本流程。',
              })
              return
            }
            if (
              paymentReceivedType === REPAYMENT_TYPE.recovery &&
              securityDetail?.recoverableAmount > 0
            ) {
              errorDialog({
                title: '提交',
                content: '该事件“可追偿金额”≤0，无需提交流程！',
              })
              return
            }
            const params = {
              ...transformSubmitData(values),
            }
            loader?.show()
            const res =
              mode === PAGE_MODE.new
                ? await createSecurityReimbursement(params)
                : await modifySecurityReimbursementById(query?.id || '', params)
            if (res.data) {
              message.success('提交成功')
              navigate(-1)
            }
          } catch (error) {
            console.error(error)
          } finally {
            loader?.hide()
          }
        },
      })
    } catch (e) {
      scrollToFirstErrorField({
        errorInfo: e,
        generateFormItemClassFn: generateFormItemClass,
      })
      loader?.hide()
    }
  }

  const onSaveDraft = async () => {
    try {
      loader?.show()
      const values = await form.getFieldsValue()
      const params = {
        ...transformSubmitData(values),
        ...(mode === PAGE_MODE.edit && { id: query?.id }),
      }
      const res = await saveSecurityReimbursementDraft(params)
      if (res.data) {
        message.success('保存草稿成功')
        navigate(-1)
      }
      loader?.hide()
    } catch (e) {
      loader?.hide()
    }
  }

  const handleCancel = () => {
    Modal.confirm({
      title: '取消',
      content: '数据尚未保存，确定离开当前页面吗？',
      onOk: () => {
        navigate(-1)
      },
    })
  }

  useEffect(() => {
    if (!ehrLoading && !dhrLoading && mode !== PAGE_MODE.new) {
      getReimbursement()
    }
    if (mode === PAGE_MODE.new) {
      form.setFieldsValue({
        eventId: query?.id,
        eventUserName:
          query?.userName && query?.userNum ? `${query?.userName}(${query?.userNum})` : '',
        paymentReceivedDate: moment().format('YYYY-MM-DD'),
        employeePayableAmount: query?.employeePayableAmount,
        employeeActualPayableAmount: query?.employeeActualPayableAmount,
        recoverableAmount: query?.recoverableAmount,
        insuranceEstimatedAmount: query?.insuranceEstimatedAmount,
      })
    }
  }, [ehrLoading, dhrLoading])

  if ((ehrLoading || dhrLoading || pageLoading) && mode !== PAGE_MODE.new) {
    return <PageLoading style={{ minHeight: 'calc(100vh - 300px)', backgroundColor: '#fff' }} />
  }

  return (
    <>
      {(mode === PAGE_MODE.edit || mode === PAGE_MODE.new) && (
        <>
          <ReimbursementForm form={form} ehrDict={ehrDict} dhrDict={dhrDict} />
          <Footer>
            <Space>
              <Button onClick={handleCancel}>取消</Button>
              <Button onClick={onSaveDraft}>保存草稿</Button>
              <Button type="primary" onClick={onSave}>
                提交
              </Button>
            </Space>
          </Footer>
        </>
      )}
      {mode === PAGE_MODE.detail && (
        <ReimbursementDetailForm form={form} ehrDict={ehrDict} dhrDict={dhrDict} />
      )}
    </>
  )
}

export default ReimbursementDetail
