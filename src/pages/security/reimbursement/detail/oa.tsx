import React, { useEffect, useRef } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { resolveQuery } from '@galaxy/utils'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import PageLoading from '@/components/pageLoading'
import { getSecurityReimbursementOAById } from '@/api/security-reimbursement'
import { ReimbursementDetailForm } from '../Form'
import { transformDataToFormData } from '../Form/utils'

export const TOOA_EVENT = 'dhrPageCompleted'

const OAReimbursementDetail = () => {
  const form = SchemaForm.createForm()
  const query = resolveQuery()
  const contentRef = useRef<HTMLDivElement>(null)
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()

  const setOaPageHeight = () => {
    window.top?.postMessage(
      {
        action: TOOA_EVENT,
        height: contentRef.current?.offsetHeight || 1000,
      },
      '*',
    )
  }

  const getReimbursement = async () => {
    const res = await getSecurityReimbursementOAById(query?.id || '')
    const data = res.data || {}
    const values = transformDataToFormData(data)
    form.setFieldsValue({ ...values })
    setTimeout(() => setOaPageHeight())
  }

  useEffect(() => {
    if (!dhrLoading && !ehrLoading) {
      getReimbursement()
      setOaPageHeight()
    }
  }, [ehrLoading, dhrLoading])

  if (ehrLoading || dhrLoading) {
    return <PageLoading style={{ height: '100vh', backgroundColor: '#fff' }} />
  }

  return (
    <div ref={contentRef}>
      <ReimbursementDetailForm form={form} ehrDict={ehrDict} dhrDict={dhrDict} />
    </div>
  )
}

export default OAReimbursementDetail
