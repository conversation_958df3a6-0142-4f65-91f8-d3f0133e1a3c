import React from 'react'
import { Observer, SchemaType } from '@amazebird/schema-form'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { isMobile } from '@galaxy/utils'
import { keys, map } from 'lodash-es'
import { REPAYMENT_TYPE } from '@/constants'
import {
  numberAmountField,
  createBaseFieldsSchema,
  createTextAreaField,
  attachmentField,
  EllipsisText,
} from '../utils'
import { detailNumberField } from '../detail/components/schema'

export const getSchema: (type: SchemaConfigType) => SchemaType = (type) => {
  const baseFields = createBaseFieldsSchema()

  const schema: SchemaType = {
    ...baseFields,
    paymentReceivedDate: {
      label: '回款日期',
      component: 'InputNumber',
      mode: 'detail',
    },
    paymentReceivedType: {
      label: '回款类型',
      component: 'DictSelect',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_REPAYMENT_TYPE?.list || []
          }
          return []
        },
      }),
      props: {
        allowClear: true,
      },
    },
    insuranceEstimatedAmount: {
      label: '保险预估金额',
      ...detailNumberField,
      visible: Observer({
        watch: 'paymentReceivedType',
        action: (paymentReceivedType) => {
          return paymentReceivedType === REPAYMENT_TYPE.insurance
        },
      }),
    },
    insuranceActualCompensationAmount: {
      label: '保险实际赔付金额',
      ...numberAmountField,
      rules: Observer({
        watch: 'paymentReceivedType',
        action: (paymentReceivedType) => {
          return paymentReceivedType === REPAYMENT_TYPE.insurance
            ? [{ required: true, message: '请输入保司预估金额' }]
            : []
        },
      }),
      visible: Observer({
        watch: 'paymentReceivedType',
        action: (paymentReceivedType) => {
          return paymentReceivedType === REPAYMENT_TYPE.insurance
        },
      }),
    },
    employeePayableAmount: {
      label: '员工应承担金额',
      ...detailNumberField,
      visible: Observer({
        watch: 'paymentReceivedType',
        action: (paymentReceivedType) => {
          return (
            paymentReceivedType === REPAYMENT_TYPE.employee ||
            paymentReceivedType === REPAYMENT_TYPE.recovery
          )
        },
      }),
    },
    employeeRepaidAmount: {
      label: '员工已还款金额',
      ...detailNumberField,
      visible: Observer({
        watch: 'paymentReceivedType',
        action: (paymentReceivedType) => {
          return paymentReceivedType === REPAYMENT_TYPE.employee
        },
      }),
    },
    employeeCurrentPaymentReceived: {
      label: '员工本次回款金额',
      ...numberAmountField,
      visible: Observer({
        watch: 'paymentReceivedType',
        action: (paymentReceivedType) => {
          return paymentReceivedType === REPAYMENT_TYPE.employee
        },
      }),
    },
    employeeActualPayableAmount: {
      label: '员工实际承担金额',
      ...detailNumberField,
      visible: Observer({
        watch: 'paymentReceivedType',
        action: (paymentReceivedType) => {
          return paymentReceivedType === REPAYMENT_TYPE.recovery
        },
      }),
    },
    recoverableAmount: {
      label: '可追偿金额',
      ...detailNumberField,
      visible: Observer({
        watch: 'paymentReceivedType',
        action: (paymentReceivedType) => {
          return paymentReceivedType === REPAYMENT_TYPE.recovery
        },
      }),
    },
    recoupedAmount: {
      label: '已追回金额',
      ...numberAmountField,
      visible: Observer({
        watch: 'paymentReceivedType',
        action: (paymentReceivedType) => {
          return paymentReceivedType === REPAYMENT_TYPE.recovery
        },
      }),
    },
    expenseDescription: createTextAreaField('费用情况说明'),
    attachment: {
      ...attachmentField,
      required: true,
    },
  }
  if (type === SchemaConfigType.Detail) {
    map(keys(schema), (key) => {
      schema[key].mode = 'detail'
      schema[key].required = false
      if (schema[key].component !== 'InputNumber') {
        schema[key].renderItem = () => (props) => {
          let ellipsis = !['expenseDescription'].includes(key)
          if (isMobile()) {
            ellipsis = false
          }
          return <EllipsisText {...props} ellipsis={ellipsis} />
        }
      }
    })
  }
  return schema
}
