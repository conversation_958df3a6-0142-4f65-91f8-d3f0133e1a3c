import React, { useEffect, useMemo } from 'react'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { useStateCenter } from '@amazebird/schema-form'
import { StateCenterDecorator } from '@amazebird/utils'
import { getSchema } from '../schema'
import style from '../index.module.less'
import SimpleSecurityForm from '../../components/SimpleSecurityForm'

type IProps = {
  form?: any
  ehrDict?: any
  dhrDict?: any
}
function EditForm(props: IProps) {
  const { form, ehrDict, dhrDict } = props
  const schema = useMemo(() => getSchema(SchemaConfigType.Edit), [])
  const ctx = useStateCenter()

  useEffect(() => {
    if (ehrDict) {
      ctx.setState({
        ehrDict,
      })
    }
    if (dhrDict) {
      ctx.setState({
        dhrDict,
      })
    }
  }, [ehrDict, dhrDict])

  const basicFields = [
    { name: 'eventId' },
    { name: 'eventUserName' },
    { name: 'paymentReceivedDate' },
    { name: 'paymentReceivedType' },
    { name: 'insuranceEstimatedAmount' },
    { name: 'insuranceActualCompensationAmount' },
    { name: 'employeePayableAmount' },
    { name: 'employeeRepaidAmount' },
    { name: 'employeeCurrentPaymentReceived' },
    { name: 'employeeActualPayableAmount' },
    { name: 'recoverableAmount' },
    { name: 'recoupedAmount' },
  ]

  return (
    <SimpleSecurityForm
      form={form}
      pageName="security-reimbursement"
      schema={schema}
      className={style.reimbursementForm}
      basicFields={basicFields}
      descriptionField={{ name: 'expenseDescription' }}
      attachmentField={{ name: 'attachment' }}
      wrapperClassName={style.innerWrapper}
    />
  )
}

export default StateCenterDecorator()(EditForm)
