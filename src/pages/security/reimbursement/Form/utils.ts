// 数据转换工具函数

import { cloneDeep, map, pick } from 'lodash-es'
import moment from 'moment'

// 将表单数据转换生成提交数据
export const transformSubmitData = (values: Record<string, any>) => {
  const newValues = cloneDeep(values)
  map(values, (v: any, key: string) => {
    switch (key) {
      case 'paymentReceivedDate':
        newValues[key] = v ? moment(v).valueOf() : undefined
        break
      case 'eventUserName':
        if (v && v.includes('(')) {
          newValues.eventUserName = v.split('(')[0]
          newValues.eventUserNum = v.split('(')[1]?.split(')')[0]
        } else {
          newValues.eventUserName = v
          newValues.eventUserNum = '00000'
        }
        break
      case 'attachment':
        newValues[key] =
          v && v.length > 0
            ? JSON.stringify(map(v, (item) => pick(item, ['uuid', 'name'])))
            : undefined
        break
    }
  })
  return newValues
}

// 将服务端数据转成表单数据
export const transformDataToFormData = (values?: any) => {
  if (!values) {
    return values
  }
  const newValues = cloneDeep(values) as any
  map(values, (v: any, key: string) => {
    switch (key) {
      case 'eventUserName':
        newValues[key] = v
          ? `${v}${values.eventUserNum !== '00000' ? `(${values.eventUserNum})` : ''}`
          : '--'
        break
      case 'paymentReceivedDate':
        newValues[key] = v ? moment(v).format('YYYY-MM-DD') : undefined
        break
      case 'attachment':
        newValues[key] = v ? JSON.parse(v) : undefined
        break
    }
    if (v === '' || v === null) {
      newValues[key] = undefined
    }
  })
  return newValues
}
