import React, { useMemo, useState, useEffect } from 'react'
import { Item, FormItemGrid } from '@amazebird/antd-schema-form'
import { <PERSON><PERSON>, Card } from 'antd'
import { StateCenterDecorator } from '@amazebird/utils'
import { keys } from 'lodash-es'
import useBatchPackDownload from '@/hooks/useBatchPackDownload'
import { useStateCenter } from '@amazebird/schema-form'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { SecurityFormWrapper, FormCard } from '../../components/index'
import { createFormItemConfig } from '../../utils'
import style from '../index.module.less'
import { getSchema } from '../schema'
import '@amazebird/antd-business-field'

type IProps = {
  form?: any
  ehrDict?: Record<string, any>
  dhrDict?: Record<string, any>
  batchPackDownload?: () => void
}

function EditForm(props: IProps) {
  const { form, ehrDict, dhrDict } = props
  const { generateFormItemConfig } = useMemo(() => createFormItemConfig('security-loan'), [])
  const [hasAttachment, setHasAttachment] = useState(false)
  const ctx = useStateCenter()
  const { batchPackDownload } = useBatchPackDownload()

  const schema = useMemo(() => getSchema(SchemaConfigType.Edit), [])
  useEffect(() => {
    if (ehrDict) {
      ctx.setState({
        ehrDict,
      })
    }
    if (dhrDict) {
      ctx.setState({
        dhrDict,
      })
    }
  }, [ehrDict, dhrDict])

  useEffect(() => {
    if (form) {
      const values = form.getFieldsValue()
      setHasAttachment(values?.attachment?.length > 0)
    }
  }, [form])

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      if (changedValues[key] && changedValues[key].length > 0 && !hasAttachment) {
        setHasAttachment(true)
      } else if ((!changedValues[key] || changedValues[key].length <= 0) && hasAttachment) {
        setHasAttachment(false)
      }
    }
  }

  // 基本信息字段
  const basicInfoFields = [
    { name: 'eventId' },
    { name: 'loanUser' },
    { name: 'loanType' },
    { name: 'isLoanStaffInjury' },
  ]

  // 转账信息字段
  const transferInfoFields = [
    { name: 'transferAccountType' },
    { name: 'accountName' },
    { name: 'transferBankAccount' },
    { name: 'transferBank' },
  ]

  return (
    <SecurityFormWrapper
      form={form}
      pageName="security-loan"
      schema={schema}
      className={style.loanForm}
      onValuesChange={onValuesChange}
      attachmentExtra={
        <Button
          type="link"
          disabled={!hasAttachment}
          onClick={() => {
            const attachment = form.getFieldValue('attachment')
            const fileName = 'loan'
            batchPackDownload(attachment, fileName)
          }}
        >
          打包下载
        </Button>
      }
    >
      {/* 基本信息卡片 */}
      <FormCard
        title="基本信息"
        fields={basicInfoFields}
        generateFormItemConfig={generateFormItemConfig}
      />

      {/* 费用明细卡片 */}
      <Card title="费用明细" size="default">
        <FormItemGrid fillEmpty colCount={2}>
          <Item
            key="estimatedMedicalExpense"
            {...generateFormItemConfig('estimatedMedicalExpense')}
          />
          <Item key="currentLoanAmount" {...generateFormItemConfig('currentLoanAmount')} />
          <Item
            key="currentLoanAmountInWords"
            {...generateFormItemConfig('currentLoanAmountInWords')}
          />
          <Item key="loanAmount" {...generateFormItemConfig('loanAmount')} />
        </FormItemGrid>
        <Item
          {...generateFormItemConfig('loanReason')}
          wrapperCol={{ span: 17 }}
          labelCol={{ span: 4 }}
        />
        <FormItemGrid fillEmpty colCount={2}>
          <Item key="repaidPlanType" {...generateFormItemConfig('repaidPlanType')} />
          <Item key="startMonthPay" {...generateFormItemConfig('startMonthPay')} />
          <Item key="planRepaidAmount" {...generateFormItemConfig('planRepaidAmount')} />
        </FormItemGrid>
      </Card>

      {/* 转账信息卡片 */}
      <FormCard
        title="转账信息"
        fields={transferInfoFields}
        generateFormItemConfig={generateFormItemConfig}
      >
        {/* 情况说明 */}
        <Item
          {...generateFormItemConfig('description')}
          wrapperCol={{ span: 17 }}
          labelCol={{ span: 4 }}
        />
      </FormCard>
    </SecurityFormWrapper>
  )
}

export default StateCenterDecorator()(EditForm)
