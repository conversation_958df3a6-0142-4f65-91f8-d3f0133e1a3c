import React, { useEffect, useMemo, useState } from 'react'
import { Item, SchemaForm } from '@amazebird/antd-schema-form'
import { But<PERSON>, Card, Col, Row } from 'antd'
import { keys, map } from 'lodash-es'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { useStateCenter } from '@amazebird/schema-form'
import { StateCenterDecorator } from '@amazebird/utils'
import AttachmentList from '@/components/AttachmentList'
import { isMobile } from '@galaxy/utils'
import { createFormItemConfig, createDetailFormUtils } from '../../utils'
import style from '../index.module.less'
import { getSchema } from '../schema'
import '@amazebird/antd-business-field'

type IProps = {
  form?: any
  ehrDict?: Record<string, any>
  dhrDict?: Record<string, any>
  batchPackDownload?: () => void
}

function DetailForm(props: IProps) {
  const { form, batchPackDownload, ehrDict, dhrDict } = props
  const schema = useMemo(() => getSchema(SchemaConfigType.Detail), [])
  const { generateFormItemConfig } = useMemo(() => createFormItemConfig('security-loan'), [])
  const { renderItem, renderSingleItem } = useMemo(
    () => createDetailFormUtils(generateFormItemConfig),
    [generateFormItemConfig],
  )
  const [hasAttachment, setHasAttachment] = useState(false)
  const [attachmentList, setAttachmentList] = useState([])
  const ctx = useStateCenter()

  // 监听表单值变化，更新附件列表
  useEffect(() => {
    const currentAttachment = form.getFieldValue('attachment') || []
    setAttachmentList(currentAttachment)
    setHasAttachment(currentAttachment.length > 0)
  }, [form])

  // 监听表单数据初始化
  useEffect(() => {
    const updateAttachmentList = () => {
      const currentAttachment = form.getFieldValue('attachment') || []
      setAttachmentList(currentAttachment)
      setHasAttachment(currentAttachment.length > 0)
    }

    // 延迟执行以确保表单数据已加载
    const timer = setTimeout(updateAttachmentList, 100)
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    if (ehrDict) {
      ctx.setState({
        ehrDict,
      })
    }
    if (dhrDict) {
      ctx.setState({
        dhrDict,
      })
    }
  }, [ehrDict, dhrDict])

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      const newAttachment = changedValues[key] || []
      setAttachmentList(newAttachment)
      setHasAttachment(newAttachment.length > 0)
    }
  }

  return (
    <SchemaForm
      form={form}
      schema={schema}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 10 }}
      onValuesChange={onValuesChange}
      className={style.loanForm}
    >
      {/* 基本信息卡片 */}
      <Card title="基本信息" size="default">
        <Row gutter={0} align="top">
          {map(['eventId', 'userCreate', 'departmentNameCreate', 'loanUser', 'loanType'], (key) =>
            renderItem(key),
          )}
          <Col xs={24} md={12} key="isLoanStaffInjury">
            <Item
              {...generateFormItemConfig('isLoanStaffInjury')}
              labelCol={{ flex: '200px' }}
              {...(isMobile() ? { wrapperCol: { flex: '1' } } : { wrapperCol: { span: 17 } })}
            />
          </Col>
        </Row>
      </Card>

      {/* 费用明细卡片 */}
      <Card title="费用明细" size="default">
        <Row gutter={0} align="top">
          {map(
            [
              'estimatedMedicalExpense',
              'currentLoanAmount',
              'currentLoanAmountInWords',
              'loanAmount',
            ],
            (key) => renderItem(key),
          )}
        </Row>
        {renderSingleItem('loanReason')}
        <Row gutter={0} align="top">
          {map(['repaidPlanType', 'startMonthPay', 'planRepaidAmount'], (key) => renderItem(key))}
        </Row>
      </Card>

      {/* 转账信息卡片 */}
      <Card title="转账信息" size="default">
        <Row gutter={0} align="top">
          {map(
            ['transferAccountType', 'accountName', 'transferBankAccount', 'transferBank'],
            (key) => renderItem(key),
          )}
        </Row>
        {renderSingleItem('description')}
      </Card>

      {/* 附件卡片 */}
      <Card
        title="附件"
        size="default"
        extra={
          <Button
            type="link"
            disabled={!hasAttachment}
            onClick={() => {
              batchPackDownload?.()
            }}
          >
            打包下载
          </Button>
        }
      >
        <AttachmentList fileList={attachmentList} />
      </Card>
    </SchemaForm>
  )
}

export default StateCenterDecorator()(DetailForm)
