import React from 'react'
import { Observer, SchemaType } from '@amazebird/schema-form'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { keys, map } from 'lodash-es'
import { isMobile } from '@galaxy/utils'
import { getDminStaffRelationV1SecurityEventById as getSecurityDetailById } from '@/api/securityEventAdminController'
import { LOAN_TYPE, REPAYMENT_PLAN_TYPE } from '@/constants'
import moment from 'moment'
import {
  textField,
  numberAmountField,
  createTextAreaField,
  attachmentField,
  EllipsisText,
} from '../utils'
import { detailNumberField } from '../detail/components/schema'

// 将负数处理为0的工具函数
const convertNegativeToZero = (value: number | undefined | null): number => {
  const num = value || 0
  return num < 0 ? 0 : num
}

// 计算数组总和，负数处理为0
const sumWithNegativeAsZero = (values: (number | undefined | null)[]): number => {
  return Number(
    values
      .map(convertNegativeToZero)
      .reduce((acc, curr) => acc + curr, 0)
      .toFixed(2),
  )
}

type SchemaConfigFunc = (type: SchemaConfigType) => SchemaType
export const getSchema: SchemaConfigFunc = (type) => {
  const schema: SchemaType = {
    // 基本信息部分字段
    eventId: {
      label: '事件ID',
      ...textField,
    },
    loanUser: {
      label: '借款人姓名',
      ...textField,
    },
    userCreate: {
      label: '创建人',
      ...textField,
    },
    departmentNameCreate: {
      label: '创建人部门',
      ...textField,
    },
    loanType: {
      label: '借款类型',
      component: 'DictSelect',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v && v.length > 0 && v?.[0]) {
            return v?.[0].BM_LOAN_TYPE?.list || []
          }
          return []
        },
      }),
      props: { labelInValue: true, maxTagCount: 'responsive' },
    },
    isLoanStaffInjury: {
      label: '是否属于员工受伤借款',
      component: 'Radio',
      options: [
        {
          label: '是',
          value: true,
        },
        {
          label: '否',
          value: false,
        },
      ],
    },

    // 费用明细部分字段
    estimatedMedicalExpense: {
      label: '预估医疗费',
      ...numberAmountField,
      required: true,
    },
    currentLoanAmount: {
      label: '本次借款金额',
      ...numberAmountField,
      rules: Observer({
        watch: ['estimatedMedicalExpense'],
        action: ([estimatedMedicalExpense]) => {
          const rules = [{ required: true, message: '请输入本次借款金额' }]
          if (estimatedMedicalExpense) {
            rules.push({
              validator: (rule, value, callback) => {
                if (value > estimatedMedicalExpense) {
                  callback('本次借款金额需小于等于预估医疗费')
                }
                callback()
              },
            })
          }
          return rules
        },
      }),
    },
    currentLoanAmountInWords: {
      label: '大写金额',
      ...textField,
    },
    loanAmount: {
      label: '已借款金额',
      ...detailNumberField,
      value: Observer({
        watch: ['currentLoanAmount'],
        action: async ([currentLoanAmount]) => {
          // TODO: 传递id查询详情获取计算字段
          // const detail = await getSecurityDetailById({ id:  })
          return currentLoanAmount ? 0 : undefined
        },
      }),
    },
    loanReason: createTextAreaField('借款事由', true, 1000),
    repaidPlanType: {
      label: '还款计划',
      component: 'DictSelect',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          if (v) {
            return v?.BM_REPAID_PLAN_TYPE?.list || []
          }
          return []
        },
      }),
      rules: Observer({
        watch: ['loanType'],
        action: ([loanType]) => {
          return loanType === LOAN_TYPE.employeeLoan
            ? [{ required: true, message: '请选择还款计划' }]
            : []
        },
      }),
      visible: Observer({
        watch: ['loanType'],
        action: ([loanType]) => {
          return loanType === LOAN_TYPE.employeeLoan
        },
      }),
    },
    startMonthPay: {
      label: '薪资扣款开始月份',
      component: 'DatePicker',
      props: {
        mode: 'month',
        // 不允许小于当前月份
        disabledDate: (current) => {
          return current && current < moment().startOf('month')
        },
      },
      rules: Observer({
        watch: ['repaidPlanType'],
        action: ([repaidPlanType]) => {
          return repaidPlanType === REPAYMENT_PLAN_TYPE.monthly
            ? [{ required: true, message: '请选择薪资扣款开始月份' }]
            : []
        },
      }),
      visible: Observer({
        watch: ['repaidPlanType'],
        action: ([repaidPlanType]) => {
          return repaidPlanType === REPAYMENT_PLAN_TYPE.monthly
        },
      }),
    },
    planRepaidAmount: {
      label: '计划每月还款金额',
      ...numberAmountField,
      rules: Observer({
        watch: ['repaidPlanType'],
        action: ([repaidPlanType]) => {
          return repaidPlanType === REPAYMENT_PLAN_TYPE.monthly
            ? [{ required: true, message: '请输入计划每月还款金额' }]
            : []
        },
      }),
      visible: Observer({
        watch: ['repaidPlanType'],
        action: ([repaidPlanType]) => {
          return repaidPlanType === REPAYMENT_PLAN_TYPE.monthly
        },
      }),
    },

    // 转账信息模块
    transferAccountType: {
      label: '转账类型',
      component: 'DictSelect',
      options: Observer({
        watch: ['#ehrDict'],
        action: (v) => {
          return v?.BM_TRANSFER_ACCOUNT_TYPE?.list || []
        },
      }),
      props: { labelInValue: true, maxTagCount: 'responsive' },
    },
    accountName: {
      label: '账户名称',
      component: 'Input',
      props: {
        maxLength: 50,
      },
    },
    transferBankAccount: {
      label: '银行账号',
      component: 'Input',
      rules: [
        {
          validator: (rule, value, callback) => {
            if (value && !/^[a-zA-Z0-9]{7,34}$/.test(value)) {
              callback('银行卡号应为7-34位数字/字母。')
            }
            callback()
          },
        },
      ],
    },
    transferBank: {
      label: '开户银行',
      component: 'Select',
      // TODO 调用开户行查询接口
    },
    description: createTextAreaField('情况说明', true, 1000),
    attachment: {
      ...attachmentField,
      label: '',
    },
  }

  if (type === SchemaConfigType.Detail) {
    map(keys(schema), (key) => {
      schema[key].mode = 'detail'
      schema[key].required = false
      if (schema[key].component !== 'InputNumber') {
        schema[key].renderItem = () => (props) => {
          let ellipsis = !['loanReason', 'description'].includes(key)
          if (isMobile()) {
            ellipsis = false
          }
          return <EllipsisText {...props} ellipsis={ellipsis} />
        }
      }
    })
  }

  return schema
}
