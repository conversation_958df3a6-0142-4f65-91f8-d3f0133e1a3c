import React, { useEffect, useState } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { resolveQuery } from '@galaxy/utils'
import { Button, Space, message } from 'antd'
import { useNavigate } from 'react-router-dom'
import { getEmploymentDisputeById, modifyEmploymentDisputeById } from '@/api/employment-dispute'
import Footer from '@/components/baseContainer/Footer'
import { useStore } from '@/stores'
import { useLoaderContext } from '@/components/Loader/useLoading'
import { useUcPermissionDict } from '@/hooks/useUcPermissionDict'
import { PAYMENT_STATUS, UC_DICT_KEY } from '@/constants'
import PageLoading from '@/components/pageLoading'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import useBatchPackDownload from '@/hooks/useBatchPackDownload'
import moment from 'moment'
import { DisputeForm } from '../components/DisputeForm'
import {
  scrollToFirstErrorField,
  transformDataToFormData,
  transformSubmitData,
  generateFormItemClass,
} from '../components/DisputeForm/utils'
import {
  OperatorType,
  useEmploymentDisputeOperations,
} from '../hooks/useEmploymentDisputeOperations'

const EmployDisputeDetail = () => {
  const form = SchemaForm.createForm()
  const { loader } = useLoaderContext()
  const query = resolveQuery()
  const navigate = useNavigate()
  const [disputeDetail, setDisputeDetail] = useState<any>()
  const [pageLoading, setPageLoading] = useState(true)
  const setTitle = useStore((state) => state.setTitle)
  const setExtraElement = useStore((state) => state.setExtra)
  const { batchPackDownload: batchPackDownloadFn } = useBatchPackDownload()
  const { data: ucDict, isLoading } = useUcPermissionDict({
    authKey: [UC_DICT_KEY.EMPLOYMENT_DISPUTE_AUTH_FIELD, UC_DICT_KEY.DEPT_AUTH],
  })
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()
  const { operatorClick } = useEmploymentDisputeOperations({
    record: disputeDetail,
  })

  const batchPackDownload = () => {
    const attachment = form.getFieldValue('attachment')
    const fileName = `${disputeDetail?.userName}_${disputeDetail?.id}_${moment().valueOf()}`
    batchPackDownloadFn(attachment, fileName)
  }

  const getEmploymentDispute = async () => {
    try {
      const res = await getEmploymentDisputeById({ id: query?.id })
      const data = res.data || {}
      const values = transformDataToFormData(data)
      form.setFieldsValue({ ...values })
      const title =
        values?.userName && values?.userNum ? `${values?.userName}(${values?.userNum})` : ''
      if (title) {
        setTitle(title)
      }
      setDisputeDetail(data)
      setPageLoading(false)
    } catch (e) {
      setPageLoading(false)
    }
  }

  const onSave = async () => {
    try {
      loader?.show()
      const values = await form.validateFields()
      const params = {
        id: disputeDetail?.id,
        ...transformSubmitData(values),
      }
      await modifyEmploymentDisputeById(params)
      message.success('提交成功')
      navigate(-1)
      loader?.hide()
    } catch (e) {
      scrollToFirstErrorField({
        errorInfo: e,
        generateFormItemClassFn: generateFormItemClass,
      })
      loader?.hide()
    }
  }

  useEffect(() => {
    // TODO: 设置权限
    setExtraElement(
      <>
        <Button type="primary" ghost onClick={() => operatorClick(OperatorType.compensationApply)}>
          赔偿额度申请
        </Button>
        <Button
          type="primary"
          ghost
          onClick={() => operatorClick(OperatorType.oaPay)}
          disabled={[PAYMENT_STATUS.approved, PAYMENT_STATUS.inReview].includes(
            disputeDetail?.paymentStatus,
          )}
        >
          用工争议付款申请
        </Button>
      </>,
    )

    return () => {
      setExtraElement(undefined)
    }
  }, [disputeDetail, operatorClick])

  useEffect(() => {
    if (!isLoading && !ehrLoading && !dhrLoading) {
      getEmploymentDispute()
    }
  }, [isLoading, ehrLoading, dhrLoading])

  if (isLoading || ehrLoading || dhrLoading || pageLoading) {
    return <PageLoading style={{ minHeight: 'calc(100vh - 300px)', backgroundColor: '#fff' }} />
  }

  return (
    <>
      <DisputeForm
        form={form}
        ucDict={ucDict}
        ehrDict={ehrDict}
        dhrDict={dhrDict}
        batchPackDownload={batchPackDownload}
      />
      <Footer>
        <Space>
          <Button
            onClick={() => {
              navigate(-1)
            }}
          >
            取消
          </Button>
          <Button type="primary" onClick={onSave}>
            提交
          </Button>
        </Space>
      </Footer>
    </>
  )
}

export default EmployDisputeDetail
