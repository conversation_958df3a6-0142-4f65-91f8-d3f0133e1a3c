import React, { useEffect, useRef } from 'react'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { resolveQuery } from '@galaxy/utils'
import { getEmploymentDisputeOAById } from '@/api/employment-dispute'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import PageLoading from '@/components/pageLoading'
import { DisputeDetailForm } from '../components/DisputeForm'
import { transformDataToFormData } from '../components/DisputeForm/utils'

// http://localhost:3000/dhr-employee-relations-webapp/login?appId=oa&type=2&code=e4948fe20f3e4f818544fe97f7fa5588&redirect=http%3A%2F%2Flocalhost:3000%2Fdhr-employee-relations-webapp%2Femployment-dispute-detail%3Fid%3D1745636947466387458&h5Redirect=http%3A%2F%2Flocalhost:3000%2Fdhr-employee-relations-webapp%2Femployment-dispute-detail%3Fid%3D1745636947466387458
export const TOOA_EVENT = 'dhrPageCompleted'
const OADisputeDetail = () => {
  const form = SchemaForm.createForm()
  const query = resolveQuery()
  const contentRef = useRef<HTMLDivElement>(null)
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()

  const setOaPageHeight = () => {
    window.top?.postMessage(
      {
        action: TOOA_EVENT,
        height: contentRef.current?.offsetHeight || 1000,
      },
      '*',
    )
  }

  const getEmploymentDispute = async () => {
    const res = await getEmploymentDisputeOAById({ id: query?.id })
    const data = res.data || {}
    const values = transformDataToFormData(data)
    form.setFieldsValue({ ...values })
    setTimeout(() => setOaPageHeight())
  }

  useEffect(() => {
    if (!dhrLoading && !ehrLoading) {
      getEmploymentDispute()
      setOaPageHeight()
    }
  }, [ehrLoading, dhrLoading])

  if (ehrLoading || dhrLoading) {
    return <PageLoading style={{ height: '100vh', backgroundColor: '#fff' }} />
  }

  return (
    <div ref={contentRef}>
      <DisputeDetailForm form={form} ehrDict={ehrDict} dhrDict={dhrDict} />
    </div>
  )
}

export default OADisputeDetail
