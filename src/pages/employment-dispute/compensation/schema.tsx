import React from 'react'
import { SchemaType, Observer } from '@amazebird/schema-form'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { EVENT_TYPE_OPTIONS } from '@/constants'
import { isMobile } from '@galaxy/utils'
import { keys, map } from 'lodash-es'
import { detailNumberField } from '@/pages/security/detail/components/schema'
import {
  textField,
  numberAmountField,
  createTextAreaField,
  attachmentField,
  EllipsisText,
} from '../../security/utils'

type SchemaConfigFunc = (type: SchemaConfigType) => SchemaType
export const getSchema: SchemaConfigFunc = (type: SchemaConfigType) => {
  const schema: SchemaType = {
    eventId: {
      label: '事件ID',
      ...textField,
    },
    eventDepartmentName: {
      label: '事件部门',
      ...textField,
    },
    eventUserName: {
      label: '事件员工姓名',
      ...textField,
    },
    eventType: {
      label: '事件类型',
      component: 'Select',
      mode: 'detail',
      options: EVENT_TYPE_OPTIONS,
    },
    statutorySeveranceCompensation: {
      label: '解除劳动合同经济补偿金',
      ...numberAmountField,
    },
    statutoryIllegalTerminationCompensation: {
      label: '违法解除劳动合同赔偿金',
      ...numberAmountField,
    },
    statutoryUnusedAnnualLeaveSalary: {
      label: '应休未休年休假工资报酬',
      ...numberAmountField,
    },
    statutoryYearEndOrPerformanceBonus: {
      label: '年终奖/绩效奖金',
      ...numberAmountField,
    },
    statutoryMaternityLeaveSalary: {
      label: '产假工资/生育津贴',
      ...numberAmountField,
    },
    statutoryHighTemperatureAllowance: {
      label: '高温补贴',
      ...numberAmountField,
    },
    statutoryOvertimePay: {
      label: '加班费',
      ...numberAmountField,
    },
    statutoryNonCompeteCompensation: {
      label: '竞业限制补偿金',
      ...numberAmountField,
    },
    statutoryBackPay: {
      label: '薪资补发',
      ...numberAmountField,
    },
    statutoryCompensationAmount: {
      label: '法定赔偿金额',
      value: Observer({
        watch: [
          'statutorySeveranceCompensation',
          'statutoryIllegalTerminationCompensation',
          'statutoryUnusedAnnualLeaveSalary',
          'statutoryYearEndOrPerformanceBonus',
          'statutoryMaternityLeaveSalary',
          'statutoryHighTemperatureAllowance',
          'statutoryOvertimePay',
          'statutoryNonCompeteCompensation',
          'statutoryBackPay',
        ],
        action: (v) => {
          return Number(v.reduce((acc, curr) => acc + (curr || 0), 0).toFixed(2))
        },
      }),
      ...detailNumberField,
    },
    mediationRequestedAmount: {
      label: '案件申请调解金额',
      ...numberAmountField,
    },
    compensationDescription: createTextAreaField('赔偿情况说明'),
    attachment: {
      ...attachmentField,
      label: '',
    },
  }
  if (type === SchemaConfigType.Detail) {
    map(keys(schema), (key) => {
      schema[key].mode = 'detail'
      schema[key].required = false
      if (schema[key].component !== 'InputNumber') {
        schema[key].renderItem = () => (props) => {
          let ellipsis = !['compensationDescription'].includes(key)
          if (isMobile()) {
            ellipsis = false
          }
          return <EllipsisText {...props} ellipsis={ellipsis} />
        }
      }
    })
  }
  return schema
}
