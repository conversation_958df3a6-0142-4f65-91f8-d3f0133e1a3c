import React, { useMemo, useState, useEffect } from 'react'
import { Item, FormItemGrid } from '@amazebird/antd-schema-form'
import { Button, Divider } from 'antd'
import { keys } from 'lodash-es'
import useBatchPackDownload from '@/hooks/useBatchPackDownload'
import { IUcPermissionData } from '@/hooks/useUcPermissionDict'
import { useStateCenter } from '@amazebird/schema-form'
import { SchemaConfigType } from '@/pages/employment-dispute/components/DisputeForm/schema'
import { StateCenterDecorator } from '@amazebird/utils'
import { SecurityFormWrapper, FormCard } from '../../../security/components/index'
import { createFormItemConfig } from '../../../security/utils'
import style from '../index.module.less'
import { getSchema } from '../schema'
import '@amazebird/antd-business-field'

type IProps = {
  form?: any
  ucDict?: IUcPermissionData
  ehrDict?: Record<string, any>
  dhrDict?: Record<string, any>
  batchPackDownload?: () => void
}

function EditForm(props: IProps) {
  const { ucDict, ehrDict, dhrDict, ...rest } = props
  const { generateFormItemConfig } = useMemo(() => createFormItemConfig('dispute-compensation'), [])
  const [hasAttachment, setHasAttachment] = useState(false)
  const ctx = useStateCenter()
  const { batchPackDownload } = useBatchPackDownload()

  const schema = useMemo(() => getSchema(SchemaConfigType.Edit, { ucDict }), [])
  useEffect(() => {
    if (ehrDict) {
      ctx.setState({
        ehrDict,
      })
    }
    if (dhrDict) {
      ctx.setState({
        dhrDict,
      })
    }
  }, [ehrDict, dhrDict])

  useEffect(() => {
    if (props.form) {
      const values = props.form.getFieldsValue()
      setHasAttachment(values?.attachment?.length > 0)
    }
  }, [props.form])

  const onValuesChange = (changedValues) => {
    const key = keys(changedValues)?.[0]
    if (key === 'attachment') {
      if (changedValues[key] && changedValues[key].length > 0 && !hasAttachment) {
        setHasAttachment(true)
      } else if ((!changedValues[key] || changedValues[key].length <= 0) && hasAttachment) {
        setHasAttachment(false)
      }
    }
  }

  // 基本信息字段
  const basicInfoFields = [
    { name: 'eventId' },
    { name: 'eventDepartmentName' },
    { name: 'eventUserName' },
    { name: 'eventType' },
  ]

  // 赔偿项目字段
  const compensationFields = [
    { name: 'statutorySeveranceCompensation' },
    { name: 'statutoryIllegalTerminationCompensation' },
    { name: 'statutoryUnusedAnnualLeaveSalary' },
    { name: 'statutoryYearEndOrPerformanceBonus' },
    { name: 'statutoryMaternityLeaveSalary' },
    { name: 'statutoryHighTemperatureAllowance' },
    { name: 'statutoryOvertimePay' },
    { name: 'statutoryNonCompeteCompensation' },
    { name: 'statutoryBackPay' },
  ]

  // 赔偿项目合计字段
  const compensationTotalFields = [{ name: 'statutoryCompensationAmount' }]

  // 调解费用字段
  const mediationFeeFields = [{ name: 'mediationRequestedAmount' }]

  return (
    <SecurityFormWrapper
      {...rest}
      pageName="dispute-compensation"
      schema={schema}
      className={style.compensationForm}
      onValuesChange={onValuesChange}
      attachmentExtra={
        <Button
          type="link"
          disabled={!hasAttachment}
          onClick={() => {
            const attachment = props.form?.getFieldValue('attachment')
            const fileName = 'compensation'
            batchPackDownload(attachment, fileName)
          }}
        >
          打包下载
        </Button>
      }
    >
      {/* 基本信息卡片 */}
      <FormCard
        title="基本信息"
        fields={basicInfoFields}
        generateFormItemConfig={generateFormItemConfig}
      />

      {/* 法定赔偿项目卡片 */}
      <FormCard title="法定赔偿项目" fields={[]} generateFormItemConfig={generateFormItemConfig}>
        {/* 赔偿项目 */}
        <div className={style.employeeCompensation}>
          <div className={style.subTitle}>赔偿项目</div>
          <FormItemGrid fillEmpty colCount={2}>
            {compensationFields.map((field) => (
              <Item key={field.name} {...generateFormItemConfig(field.name)} />
            ))}
          </FormItemGrid>
          <Divider />
        </div>

        {/* 赔偿项目合计 */}
        <div className={style.subTitle}>赔偿项目合计</div>
        <FormItemGrid fillEmpty colCount={2}>
          {compensationTotalFields.map((field) => (
            <Item key={field.name} {...generateFormItemConfig(field.name)} />
          ))}
        </FormItemGrid>
      </FormCard>

      {/* 调解费用卡片 */}
      <FormCard
        title="调解费用"
        fields={mediationFeeFields}
        generateFormItemConfig={generateFormItemConfig}
      >
        {/* 赔偿情况说明 */}
        <Item
          {...generateFormItemConfig('compensationDescription')}
          wrapperCol={{ span: 17 }}
          labelCol={{ span: 4 }}
        />
      </FormCard>
    </SecurityFormWrapper>
  )
}

export default StateCenterDecorator()(EditForm)
