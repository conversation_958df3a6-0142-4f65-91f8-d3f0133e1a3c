import { useEffect, useRef, useState } from 'react'
import { message } from 'antd'
import { filter as filterFn, map } from 'lodash-es'
import {
  getEmploymentDisputeList,
  getDisputeContractSubject as getDisputeContractSubjectApi,
} from '@/api/employment-dispute'
import { useLoaderContext } from '@/components/Loader/useLoading'
import { EXPORT_TASK_TYPE, UC_DICT_KEY } from '@/constants'
import { useDhrDicts, useEhrDicts } from '@/hooks/useDict'
import { useUcPermissionDict } from '@/hooks/useUcPermissionDict'
import { exportService } from '@/services/export'
import { SchemaForm } from '@amazebird/antd-schema-form'
import { SchemaTable } from '@amazebird/antd-schema-table'

export const useData = () => {
  const form: any = SchemaForm.createForm()
  const { loader } = useLoaderContext()
  const action = SchemaTable.createAction?.()
  const columnState = useRef<Record<string, any> | undefined>(undefined)
  const [pageLoading, setPageLoading] = useState(true)
  const { data: ehrDict, isLoading: ehrLoading } = useEhrDicts()
  const { data: dhrDict, isLoading: dhrLoading } = useDhrDicts()
  const { data: ucDict, isLoading: ucLoading } = useUcPermissionDict({
    authKey: [
      UC_DICT_KEY.EMPLOYMENT_DISPUTE_AUTH_FIELD,
      // UC_DICT_KEY.BM_HTZT,
      // UC_DICT_KEY.BM_BZGSQY,
      UC_DICT_KEY.DEPT_AUTH,
    ],
  })

  const getDisputeContractSubject = async () => {
    const res = await getDisputeContractSubjectApi()
    const data = res?.data || []
    const contractSubject = filterFn(ehrDict?.BM_HTZT.list, (item) => data.includes(item.value))
    form.setState({ contractSubject })
  }

  const getParams = (filter) => {
    const {
      accidentAreas,
      accidentContractSubjects,
      departmentId,
      process,
      timeAccident,
      paymentResult,
      ...rest
    } = filter
    const params = {
      ...rest,
      accidentAreas: map(accidentAreas, (item) => item.value),
      accidentContractSubjects: map(accidentContractSubjects, (item) => item.value),
      departmentId: departmentId?.value,
      process: process?.value,
      paymentResult: paymentResult?.value,
    }
    if (timeAccident && timeAccident.length === 2) {
      params.timeAccidentStart = timeAccident[0].startOf('month').valueOf()
      params.timeAccidentEnd = timeAccident[1].endOf('month').valueOf()
    }
    return params
  }

  const loadData = async ({ filter, pagination }) => {
    try {
      const { current, pageSize } = pagination
      const params = { ...getParams(filter), page: current, size: pageSize, sort: ['id,desc'] }
      const res = await getEmploymentDisputeList(params)
      console.log(res)
      return {
        data: res.data || [],
        total: res?.count || 0,
      }
    } catch (error) {
      return {
        data: [],
        total: 0,
      }
    }
  }

  const exportData = async (filter) => {
    const params = getParams(filter)
    loader?.show('正在导出数据')
    const res = await exportService.export(EXPORT_TASK_TYPE.EMPLOY_DISPUTE, params)
    if (res.success) {
      message.success('数据导出成功')
    } else {
      message.error(res.errMsg || '数据导出失败')
    }
    loader?.hide()
  }

  useEffect(() => {
    if (!ehrLoading) {
      form.setState({ ehrDict })
    }
    if (!ehrLoading && !dhrLoading && !ucLoading) {
      getDisputeContractSubject()
      setPageLoading(false)
    }
  }, [ehrLoading, dhrLoading, ucLoading])

  return { action, form, columnState, pageLoading, ehrDict, dhrDict, ucDict, loadData, exportData }
}
