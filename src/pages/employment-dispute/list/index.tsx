import React, { useMemo } from 'react'
import { SchemaTable } from '@amazebird/antd-schema-table'
import ExportModal from '@/components/ExportModal'
import modalWrapperHoc from '@/components/modalWrapperHoc'
import PageLoading from '@/components/pageLoading'
import BusinessPermission from '@/components/businessPermission'
import { EMPLOYMENT_DISPUTE } from '@/constants/rbac-code/employment-dispute'
import { useData } from './useData'
import { getColumns, getSearchColumns } from './columns'
import styles from './index.less'

const EmployDisputeList = () => {
  const { action, form, columnState, pageLoading, ehrDict, dhrDict, ucDict, loadData, exportData } =
    useData()

  const columns = useMemo(() => {
    return getColumns({ ehrDict, dhrDict, reloadList: action?.refresh })
  }, [ehrDict, dhrDict])

  const searchColumns = useMemo(() => {
    return getSearchColumns({ ucDict })
  }, [ucDict])

  const exportFn = () => {
    const searchValues = form.getFieldsValue()
    modalWrapperHoc(ExportModal)({
      searchColumns,
      searchValues,
      excludeField: ['timeAccident'],
      onExport: async (params) => {
        exportData(params)
      },
    })
  }

  if (pageLoading) {
    return (
      <div className={styles.table}>
        <PageLoading style={{ minHeight: 'calc(100vh - 300px)', backgroundColor: '#fff' }} />
      </div>
    )
  }

  return (
    <div className={styles.table}>
      <SchemaTable
        action={action}
        form={form}
        columns={columns as any}
        searchColumns={searchColumns}
        request={loadData}
        rowKey="id"
        toolbarOptions={{ setting: true }}
        columnsState={{
          persistenceType: 'localStorage',
          persistenceKey: 'employ-dispute-manage-table',
          onChange: (value) => {
            columnState.current = value
          },
        }}
        toolbar={{
          action: (
            <BusinessPermission code={EMPLOYMENT_DISPUTE.EXPORT}>
              <a onClick={exportFn}>导出</a>
            </BusinessPermission>
          ),
        }}
      />
    </div>
  )
}

export default EmployDisputeList
