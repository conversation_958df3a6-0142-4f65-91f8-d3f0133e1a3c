import React from 'react'
import TablePermissionAction, {
  PermissionActionItem,
} from '@/components/businessPermission/TablePermissionAction'
import { PAYMENT_STATUS } from '@/constants'
import { EMPLOYMENT_DISPUTE } from '@/constants/rbac-code/employment-dispute'
import {
  useEmploymentDisputeOperations,
  OperatorType,
} from '../hooks/useEmploymentDisputeOperations'

type IProps = {
  reloadList: () => void
  expandNumber?: number
  record: any
}

const BaseTableActions = (props: IProps) => {
  const { expandNumber, record, reloadList } = props
  const { operatorClick } = useEmploymentDisputeOperations({ record, reloadList })

  const getOperatorColumns = () => {
    const actions: PermissionActionItem[] = [
      {
        key: OperatorType.detail,
        label: '查看',
        order: 1,
        permissionCode: EMPLOYMENT_DISPUTE.DETAIL,
      },
      {
        key: OperatorType.oaPay,
        label: '用工争议付款申请',
        order: 3,
        permissionCode: EMPLOYMENT_DISPUTE.DISPUTE_PAYMENT,
        disabled: [PAYMENT_STATUS.approved, PAYMENT_STATUS.inReview].includes(record.paymentStatus),
      },
      {
        key: OperatorType.compensationApply,
        label: '赔偿额度申请',
        order: 2,
        // TODO 修改权限码
        permissionCode: EMPLOYMENT_DISPUTE.DETAIL,
      },
    ]

    return actions
  }

  return (
    <TablePermissionAction
      expandNumber={expandNumber}
      actions={getOperatorColumns()}
      menuClick={operatorClick}
    />
  )
}

export default BaseTableActions
