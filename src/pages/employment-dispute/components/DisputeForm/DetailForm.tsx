import { SchemaForm, Item } from '@amazebird/antd-schema-form'
import React, { useEffect, useMemo } from 'react'
import { Row, Col, Card } from 'antd'
import { useStateCenter } from '@amazebird/schema-form'
import '@amazebird/antd-business-field'
import { map } from 'lodash-es'
import { isMobile } from '@galaxy/utils'
import { SchemaConfigType, getSchema } from './schema'
import styles from './index.less'
import { generateFormItemConfig } from './utils'

type IProps = {
  // eslint-disable-next-line react/no-unused-prop-types
  form?: any
  ehrDict?: Record<string, any>
  dhrDict?: Record<string, any>
}

export default function DetailForm(props: IProps) {
  const { ehrDict, dhrDict, ...rest } = props
  const ctx = useStateCenter()
  const basicProps = {
    labelCol: { flex: '135px' },
    ...(isMobile() ? { wrapperCol: { flex: '1' } } : { wrapperCol: { span: 17 } }),
  }
  const schema = useMemo(() => getSchema(SchemaConfigType.Detail), [])
  const renderItem = (key: string) => {
    return (
      <Col xs={24} md={12} key={key}>
        <Item {...generateFormItemConfig(key)} {...basicProps} />
      </Col>
    )
  }

  useEffect(() => {
    if (ehrDict) {
      ctx.setState({
        ehrDict,
      })
    }
    if (dhrDict) {
      ctx.setState({
        dhrDict,
      })
    }
  }, [ehrDict, dhrDict])

  return (
    <SchemaForm
      {...rest}
      schema={schema}
      layout="horizontal"
      labelCol={{ span: 8 }}
      wrapperCol={{ span: 10 }}
      className={styles.disputeDetailForm}
    >
      <Row>
        <Col span="24">
          <Card title="员工信息" size="default">
            <Row gutter={0} align="top">
              {map(
                [
                  'userName',
                  'departmentFullName',
                  'firstLevelDepartment',
                  'secondLevelDepartment',
                  'thirdLevelDepartment',
                  'fourthLevelDepartment',
                  'positionName',
                  'empFormName',
                  'empCategoryName',
                  'contractTypeName',
                  // 'userNameUpdate',
                  // 'timeUpdate',
                  'oaNo',
                  'disputeId',
                ],
                (key) => renderItem(key),
              )}
            </Row>
          </Card>
          <Card title="案件金额" size="default">
            <Row gutter={0} align="top">
              {map(
                [
                  'empClaimAmount',
                  'eventClosureAmount',
                  'companyBearingAmount',
                  'empBearingAmount',
                  'commercialInsuranceAmount',
                  'statutoryStandardAmount',
                  'eventCategorySalaryId',
                  'isRequiredPayment',
                  'payAmount',
                  'timeScheduledPayment',
                  'timeActualPayment',
                  'paymentResult',
                  'paymentEntity',
                  'payeeName',
                  'payeeBank',
                  'payeeBankAccount',
                ],
                (key) => renderItem(key),
              )}
            </Row>
          </Card>
          <Card title="案件信息" size="default">
            <Row gutter={0} align="top">
              {map(
                [
                  'arbitrationSubject',
                  'subjectName',
                  'timeEvent',
                  'timeIncidentYearMonth',
                  'eventLocationId',
                  'isDepartmentDuty',
                ],
                (key) => renderItem(key),
              )}
            </Row>
            <Item
              {...generateFormItemConfig('deptResponsibleSituation')}
              // wrapperCol={{ span: 17 }}
              // labelCol={basicProps.labelCol}
              {...{ ...basicProps }}
            />
            <Row gutter={0} align="top">
              {map(['eventTypeLevelFirstId', 'eventTypeLevelSecondId'], (key) => renderItem(key))}
            </Row>
            <Item
              {...generateFormItemConfig('eventContent')}
              // wrapperCol={{ span: 17 }}
              // labelCol={basicProps.labelCol}
              {...{ ...basicProps }}
            />
            <Row gutter={0} align="top">
              {map(['isSpecial', 'otherEventTypeDesc'], (key) => renderItem(key))}
            </Row>
            <Item
              {...generateFormItemConfig('remark')}
              // wrapperCol={{ span: 17 }}
              // labelCol={basicProps.labelCol}
              {...{ ...basicProps }}
            />
          </Card>
          <Card title="案件进程" size="default">
            <Row gutter={0} align="top">
              {map(
                ['sscHandler', 'acceptChannelId', 'currentStageProcessId', 'timeEventClosure'],
                (key) => renderItem(key),
              )}
            </Row>
            <Item
              {...generateFormItemConfig('progressDetails')}
              // wrapperCol={{ span: 17 }}
              // labelCol={basicProps.labelCol}
              {...{ ...basicProps }}
            />
          </Card>
          <Card title="案件结果" size="default">
            <Row gutter={0} align="top">
              {map(
                [
                  'eventResultId',
                  'eventNum',
                  'eventResultDetails',
                  'dataSource',
                  'terminationTypeId',
                  'isEconCompInvolvement',
                ],
                (key) => renderItem(key),
              )}
            </Row>
          </Card>
        </Col>
      </Row>
    </SchemaForm>
  )
}
