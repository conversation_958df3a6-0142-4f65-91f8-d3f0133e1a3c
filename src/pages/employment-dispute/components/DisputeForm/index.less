.disputeForm {
  max-height: calc(100vh - 350px);

  .basicInfo {
    background-color: #ffffff;

    :global {
      .@{ant-prefix}-form-item {
        margin-bottom: 0px;
      }
    }

    .lastCol {
      margin-bottom: 24px;
    }
  }

  .subTitle {
    font-weight: bold;
    margin-bottom: 16px;
  }

  .anchorForm {
    padding: 16px;

    :global {
      .editor_Anchor_anchors {
        height: calc(100vh - 390px);
      }

      .editor_Anchor_anchorContextBox {
        background-color: initial;
        padding: 0;
      }

      .editor_Anchor_anchorItemBox {
        margin-bottom: 16px;
      }

      .@{ant-prefix}-form-item {
        .@{ant-prefix}-picker {
          width: 100%;
        }
        .@{ant-prefix}-input-number-group-wrapper {
          width: 100%;
        }
      }
    }

    [data-type='AnchorItem']:last-child {
      margin-bottom: 0;
    }
  }
}

.disputeDetailForm {
  background-color: #f0f0f0;

  :global {
    .@{ant-prefix}-card {
      margin-bottom: 16px;
    }

    .@{ant-prefix}-form-item {
      margin-bottom: 12px;
    }

    .@{ant-prefix}-form-item-label > label {
      color: #9a9a9a;
      height: 24px;
    }

    .@{ant-prefix}-form-item-control-input {
      min-height: 24px;
    }
  }
}
