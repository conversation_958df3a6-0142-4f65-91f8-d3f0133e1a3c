import { message } from 'antd'
import { useNavigate } from 'react-router-dom'
import { confirmDialog } from '@/components/Dialog'
import { PAGE_MODE } from '@/constants'
import { addOaDisputePayment } from '@/api/employment-dispute'
import { createCompensationCheck } from '@/api/compensation'
import { createParamsWithValues } from '@/pages/security/hooks/useSecurityOperations'

export enum OperatorType {
  detail = 'detail', // 查看
  oaPay = 'oaPay', // 发起付款
  compensationApply = 'compensation_apply', // 赔偿额度申请
}

interface UseEmploymentDisputeOperationsProps {
  record?: any
  reloadList?: () => void
}

export const useEmploymentDisputeOperations = ({
  record,
  reloadList,
}: UseEmploymentDisputeOperationsProps = {}) => {
  const navigate = useNavigate()

  const handleOaPay = () => {
    confirmDialog({
      title: '确定要发起付款吗？',
      content: '确定后，会向OA发起付款确认流程',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const res = await addOaDisputePayment({ id: record.id })
        if (res.data) {
          reloadList?.()
          message.success('发起付款成功')
        } else {
          message.error('发起付款失败')
        }
      },
    })
  }

  const handleCompensationApply = async () => {
    try {
      await createCompensationCheck({
        eventId: record?.id,
        // 源 1安全事件 2用工争议
        source: 2,
      })
      const searchParams = new URLSearchParams(
        createParamsWithValues({
          mode: PAGE_MODE.new,
          id: record?.id,
          userName: record?.userName,
          userNum: record?.userNum,
          accidentDepartmentName: record?.accidentDepartmentName,
        }),
      )
      navigate(`/employment-dispute/compensation?${searchParams.toString()}`)
    } catch (error) {
      // 使用全局的message服务端错误信息
    }
  }

  const operatorClick = (key: string) => {
    switch (key) {
      case OperatorType.detail:
        if (record?.id) {
          navigate(`/employment-dispute/detail?id=${record.id}`)
        }
        break
      case OperatorType.oaPay:
        handleOaPay()
        break
      case OperatorType.compensationApply:
        handleCompensationApply()
        break
      default:
        break
    }
  }

  return {
    operatorClick,
    handleOaPay,
    handleCompensationApply,
  }
}
