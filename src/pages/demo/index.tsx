import { Button } from 'antd'
import React from 'react'
import JS<PERSON><PERSON> from 'jszip'
import { saveAs } from 'file-saver'

const index = () => {
  const fileLinks = [
    {
      url: 'https://img1.baidu.com/it/u=1828501987,902472408&fm=253&fmt=auto&app=120&f=JPEG?w=731&h=500',
      name: '1.jpeg',
    },
    {
      url: 'https://img1.baidu.com/it/u=1828501987,902472408&fm=253&fmt=auto&app=120&f=JPEG?w=731&h=500',
      name: '2.jpeg',
    },
  ]

  const download = () => {
    const zip = new JSZip()

    // 使用 Promise.all 来异步获取文件内容
    Promise.all(
      fileLinks.map((file) =>
        fetch(file.url)
          .then((response) => response.arrayBuffer())
          .then((buffer) => zip.file(file.name, new Uint8Array(buffer))),
      ),
    )
      .then(() => {
        // 生成压缩文件
        return zip.generateAsync({ type: 'blob' })
      })
      .then(function (content) {
        // 使用 FileSaver.js 触发文件下载
        saveAs(content, '打包文件.zip')
      })
  }
  return (
    <div>
      <Button onClick={download}>打包下载</Button>
    </div>
  )
}

export default index
