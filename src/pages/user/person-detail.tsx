/* eslint-disable global-require */
/* eslint-disable import/no-dynamic-require */
import React from 'react'
import { Form } from 'antd'
import userService from '@/mainApp/services/userService'
import style from './style.module.less'

const formItemLayout = {
  labelCol: {
    span: 6,
  },
  wrapperCol: {
    span: 14,
  },
}

const personDetail = () => {
  const formatPhone = (string: string | number) => {
    const formatReg = new RegExp('(\\w{3})(\\d+)(\\d{4})', 'g')
    if (string) {
      return `${string}`.replace(/\s/g, '').replace(formatReg, '$1****$3')
    }
    return ''
  }

  return (
    <Form
      className={style.personDetail}
      name="validate_other"
      {...formItemLayout}
      initialValues={{
        'input-number': 3,
        'checkbox-group': ['A', 'B'],
        rate: 3.5,
      }}
    >
      <Form.Item label="账号ID">
        <span className="ant-form-text">{userService.userInfo.userId}</span>
      </Form.Item>
      <Form.Item label="账号名称">
        <span className="ant-form-text">{userService.userInfo.username}</span>
      </Form.Item>
      <Form.Item label="工号">
        <span className="ant-form-text">{userService.userInfo.userNumber}</span>
      </Form.Item>
      <Form.Item label="手机号码">
        <span className="ant-form-text">{formatPhone(userService.userInfo.userPhone)}</span>
      </Form.Item>
      <Form.Item label="电子邮箱">
        <span className="ant-form-text">{userService.userInfo.email}</span>
      </Form.Item>
    </Form>
  )
}

export default personDetail
