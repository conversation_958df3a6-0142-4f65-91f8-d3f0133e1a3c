import LocalConfig from './local'
import DevConfig from './dev'
import TestConfig from './test'
import ProdConfig from './prod'
import ItPreConfig from './it-pre'

const ENV = process.env.BUILD_ENV
// eslint-disable-next-line import/no-mutable-exports
let configModule: any = {} // 用作变量导出

if (ENV === 'test') {
  configModule = TestConfig
} else if (ENV === 'prod') {
  configModule = ProdConfig
} else if (ENV === 'dev') {
  configModule = DevConfig
} else if (ENV === 'pre') {
  configModule = ItPreConfig
} else {
  configModule = LocalConfig
}

export default configModule
