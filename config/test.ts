import base from './base'

const { publicName, projectName, loginUrl } = base

export default {
  projectName,
  environment: 'itPre',
  loginUrl,
  baseRoute: `${publicName}`,
  // 企业微信
  appid: 'ww9dbeb2758faf497c',
  agentid: '1000012',
  authUrl: 'http://dhr.pre.pupufz.com/admin/account/wechat_work',
  adminState: 'admin_wechat',
  // 请求前缀
  // serverUrl: 'http://dhr.pre.pupufz.com',
  serverUrl: 'http://dhr-staff-relation-f6152542688-svc.testdhrk8s.pupufz.com',
  tenantId: 82,
  oaConfig: {
    ucAppId: 'pupu3a30db56859d7934',
    url: 'http://oa.test.pupufz.com',
  },
  loginUcAppId: 'pupu9b454b762c683d7e',
  ucAppCode: '41012',
}
